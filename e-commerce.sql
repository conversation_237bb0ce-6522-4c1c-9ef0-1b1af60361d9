CREATE DATABASE IF NOT EXISTS ecommercedb;
USE ecommercedb;
CREATE TABLE IF NOT EXISTS users (id INT AUTO_INCREMENT PRIMARY KEY , name VARCHAR(100), email VARCHAR(150));
CREATE TABLE IF NOT EXISTS  products (id INT AUTO_INCREMENT PRIMARY KEY ,name VARCHA<PERSON>(100), price DECIMAL);
CREATE TABLE IF NOT EXISTS orders (id INT AUTO_INCREMENT PRIMARY KEY , user_id INT, product_id INT, FOREIGN KEY (user_id) REFERENCES users(id), FOREIGN KEY (product_id) REFERENCES products(id), quantity INT, order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP );

-- INSERT USERS

INSERT INTO users (name, email) VALUES ('<PERSON>', '<EMAIL>');
INSERT INTO users (name, email) VALUES ('<PERSON>', '<EMAIL>');
INSERT INTO users (name, email) VALUES ('<PERSON>', '<EMAIL>');
INSERT INTO users (name, email) VALUES ('<PERSON>', '<EMAIL>');
INSERT INTO users (name, email) VALUES ('Charlie Wilson', '<EMAIL>');

-- INSERT PRODUCTS

INSERT INTO products (name, price) VALUES ('Laptop', 999.99);
INSERT INTO products (name, price) VALUES ('Mouse', 25.50);
INSERT INTO products (name, price) VALUES ('Keyboard', 75.00);
INSERT INTO products (name, price) VALUES ('Monitor',199.99);
INSERT INTO products (name, price) VALUES ('Headphones', 49.99);
INSERT INTO products (name, price) VALUES ('Tablet', 999.99);


-- INSERT ORDERS

INSERT INTO orders (user_id, product_id, quantity) VALUES (1, 1, 1);
INSERT INTO orders (user_id, product_id, quantity) VALUES (2, 2, 2);
INSERT INTO orders (user_id, product_id, quantity) VALUES (3, 3, 1);
INSERT INTO orders (user_id, product_id, quantity) VALUES (4, 4, 1);
INSERT INTO orders (user_id, product_id, quantity) VALUES (5, 5, 3);
INSERT INTO orders (user_id, product_id, quantity) VALUES (1, 6, 1);
INSERT INTO orders (user_id, product_id, quantity) VALUES (2, 1, 1);
INSERT INTO orders (user_id, product_id, quantity) VALUES (3, 5, 2);

