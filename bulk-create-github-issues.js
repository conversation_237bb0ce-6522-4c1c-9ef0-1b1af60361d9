#!/usr/bin/env node

/**
 * Bulk GitHub Issues Creator
 * 
 * This script creates GitHub issues in bulk using the GitHub REST API (Octokit).
 * It reads issue data from github-issues-data.js and creates them one by one.
 * 
 * Prerequisites:
 * 1. Install dependencies: npm install @octokit/rest dotenv
 * 2. Create a .env file with your GitHub token: GITHUB_TOKEN=your_token_here
 * 3. Update the REPO_OWNER and REPO_NAME constants below
 * 
 * Usage:
 * node bulk-create-github-issues.js
 */

const { Octokit } = require('@octokit/rest');
const { applicationLayerIssues } = require('./github-issues-data');
require('dotenv').config();

// Configuration - UPDATE THESE VALUES
const REPO_OWNER = 'your-username';  // Replace with your GitHub username or organization
const REPO_NAME = 'your-repo-name';  // Replace with your repository name

// GitHub API configuration
const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

/**
 * Create a single GitHub issue
 * @param {Object} issueData - The issue data object
 * @param {string} issueData.title - Issue title
 * @param {string} issueData.body - Issue body (markdown)
 * @param {string[]} issueData.labels - Array of label names
 * @returns {Promise<Object>} Created issue data
 */
async function createIssue(issueData) {
  try {
    console.log(`Creating issue: ${issueData.title}`);
    
    const response = await octokit.rest.issues.create({
      owner: REPO_OWNER,
      repo: REPO_NAME,
      title: issueData.title,
      body: issueData.body,
      labels: issueData.labels || [],
    });

    console.log(`✅ Created issue #${response.data.number}: ${issueData.title}`);
    return response.data;
  } catch (error) {
    console.error(`❌ Failed to create issue: ${issueData.title}`);
    console.error(`Error: ${error.message}`);
    throw error;
  }
}

/**
 * Create all issues with a delay between each creation
 * @param {Object[]} issues - Array of issue data objects
 * @param {number} delayMs - Delay in milliseconds between issue creations
 */
async function createAllIssues(issues, delayMs = 1000) {
  console.log(`Starting bulk creation of ${issues.length} issues...`);
  console.log(`Repository: ${REPO_OWNER}/${REPO_NAME}`);
  console.log(`Delay between issues: ${delayMs}ms\n`);

  const results = [];
  const errors = [];

  for (let i = 0; i < issues.length; i++) {
    const issue = issues[i];
    
    try {
      const result = await createIssue(issue);
      results.push(result);
      
      // Add delay between requests to avoid rate limiting
      if (i < issues.length - 1) {
        console.log(`Waiting ${delayMs}ms before next issue...\n`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    } catch (error) {
      errors.push({
        issue: issue.title,
        error: error.message
      });
      
      // Continue with next issue even if one fails
      console.log('Continuing with next issue...\n');
    }
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('BULK CREATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total issues to create: ${issues.length}`);
  console.log(`Successfully created: ${results.length}`);
  console.log(`Failed: ${errors.length}`);

  if (errors.length > 0) {
    console.log('\nFailed issues:');
    errors.forEach(error => {
      console.log(`- ${error.issue}: ${error.error}`);
    });
  }

  if (results.length > 0) {
    console.log('\nSuccessfully created issues:');
    results.forEach(result => {
      console.log(`- #${result.number}: ${result.title}`);
      console.log(`  URL: ${result.html_url}`);
    });
  }

  return { results, errors };
}

/**
 * Validate environment and configuration
 */
function validateConfig() {
  if (!process.env.GITHUB_TOKEN) {
    console.error('❌ GITHUB_TOKEN environment variable is required');
    console.error('Create a .env file with: GITHUB_TOKEN=your_token_here');
    process.exit(1);
  }

  if (REPO_OWNER === 'your-username' || REPO_NAME === 'your-repo-name') {
    console.error('❌ Please update REPO_OWNER and REPO_NAME in the script');
    process.exit(1);
  }

  console.log('✅ Configuration validated');
}

/**
 * Main execution function
 */
async function main() {
  try {
    validateConfig();
    
    console.log(`Found ${applicationLayerIssues.length} issues to create\n`);
    
    // Ask for confirmation
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      readline.question(`Do you want to create ${applicationLayerIssues.length} issues in ${REPO_OWNER}/${REPO_NAME}? (y/N): `, resolve);
    });
    
    readline.close();

    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('Operation cancelled.');
      return;
    }

    await createAllIssues(applicationLayerIssues);
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  createIssue,
  createAllIssues,
  validateConfig
};
