CREATE DATABASE IF NOT EXISTS `university-student-information-system`;
USE `university-student-information-system`;

-- Question 1

CREATE TABLE IF NOT EXISTS `student` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `firstname` VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    `lastname` VA<PERSON>HA<PERSON>(255) NOT NULL,
    `email` VARCHAR(255) NOT NULL,
    `age` INT NOT NULL,
    `major` VARCHAR(255) NOT NULL,
    `city` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `instructor` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `department` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `course` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(255) NOT NULL,
    `credits` INT NOT NULL,
    `instructor_id` INT NOT NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON>EY (`instructor_id`) REFERENCES `instructor`(`id`) ON DELETE CASCADE,
    PRIMARY KEY (`id`)
);

CREATE TABLE IF NOT EXISTS `enrollment` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `student_id` INT NOT NULL,
    `course_id` INT NOT NULL,
    `grade` VARCHAR(255) NOT NULL,
    `enrollment_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`student_id`) REFERENCES `student`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`course_id`) REFERENCES `course`(`id`) ON DELETE CASCADE,
    PRIMARY KEY (`id`)
);

-- Question 2

INSERT INTO `student` (`firstname`, `lastname`, `email`, `age`, `major`) VALUES
('John', 'Doe', '<EMAIL>', 20, 'Computer Science'),
('Jane', 'Smith', '<EMAIL>', 21, 'Mathematics'),
('Bob', ' Johnson', '<EMAIL>', 19, 'Physics'),
('Alice', 'Brown', '<EMAIL>', 20, 'English'),
('David', 'Lee', '<EMAIL>', 21, 'History'),
('Emily', 'Davis', '<EMAIL>', 19, 'Biology'),
('Michael', 'Wilson', '<EMAIL>', 20, 'Chemistry'),
('Sarah', 'Taylor', '<EMAIL>', 21, 'Economics'),
('William', 'Anderson', '<EMAIL>', 19, 'Political Science'),
('Jessica', 'White', '<EMAIL>', 20, 'Sociology'),
('James', 'Harris', '<EMAIL>', 21, 'Psychology'),
('Mary', 'Clark', '<EMAIL>', 19, 'Anthropology'),
('Robert', 'Lewis', '<EMAIL>', 20, 'Geography'),
('Laura', 'Walker', '<EMAIL>', 21, 'Philosophy'),
('Richard', 'Hall', '<EMAIL>', 19, 'Linguistics'),
('Susan', 'Young', '<EMAIL>', 20, 'Art History');


INSERT INTO `instructor` (`name`, `department`) VALUES
('Dr. Smith', 'Computer Science'),
('Dr. Johnson', 'Mathematics'),
('Dr. Doe', 'Physics'),
('Dr. Brown', 'English'),
('Dr. Lee', 'History'),
('Dr. Davis', 'Biology'),
('Dr. Wilson', 'Chemistry'),
('Dr. Taylor', 'Economics'),
('Dr. Anderson', 'Political Science'),
('Dr. White', 'Sociology'),
('Dr. Harris', 'Psychology'),
('Dr. Clark', 'Anthropology'),
('Dr. Lewis', 'Geography'),
('Dr. Walker', 'Philosophy'),
('Dr. Hall', 'Linguistics'),
('Dr. Young', 'Art History');

INSERT INTO `course` (`name`, `credits`, `instructor_id`) VALUES
('Database management', 4, 1),
('Algorithms', 4, 1),
('Quantum Mechanics', 4, 3),
('Linear Algebra', 4, 2),
('Calculus', 4, 5),
('English', 4, 2),
('History', 4, 6),
('Biology', 4, 2),
('Chemistry', 4, 7),
('Economics', 4, 8),
('Political Science', 4, 4),
('Sociology', 4, 3),
('Psychology', 4, 2),
('Anthropology', 4, 5),
('Geography', 4, 8),
('Philosophy', 4, 9),
('Linguistics', 4, 1),
('Art History', 4, 1);




UPDATE `course` SET `credits` = 5 WHERE `id` = 2;

DELETE FROM `student` WHERE `id` = 3;

-- Question 3

INSERT INTO `enrollment` (`student_id`, `course_id`, `grade`, `enrollment_date`) VALUES
(1, 1, 'A', '2021-01-01'),
(1, 2, 'B', '2012-05-01'),
(2, 3, 'A', '2022-05-01'),
(2, 2, 'B', '2020-05-01'),
(4, 4, 'A', '2021-05-01'),
(5, 2, 'C', '2020-05-09'),
(1, 3, 'C', '2021-05-08'),
(4, 5, 'B', '2021-05-06'),
(5, 1, 'A', '2021-05-05'),
(5, 3, 'C', '2021-05-07'),
(6, 6, 'A', '2021-05-04'),
(6, 2, 'B', '2021-05-03'),
(7, 7, 'A', '2021-05-02'),
(7, 2, 'B', '2021-05-01'),
(8, 7, 'A', '2021-05-01'),
(8, 8, 'B', '2021-05-01'),
(9, 1, 'A', '2021-05-01'),
(9, 2, 'B', '2021-05-01'),
(10, 5, 'A', '2021-05-01'),
(10, 2, 'B', '2021-05-01'),
(11, 4, 'A', '2021-05-01'),
(11, 3, 'B', '2021-05-01'),
(12, 9, 'A', '2021-05-01'),
(12, 9, 'B', '2021-05-01'),
(13, 7, 'A', '2021-05-01'),
(13, 8, 'B', '2021-05-01'),
(14, 6, 'A', '2021-05-01'),
(14, 3, 'B', '2021-05-01');

SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
WHERE
    enrollment.grade = 'A';

-- Question 4
SELECT
    student.firstname,
    student.lastname,
    course.name,
    enrollment.grade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id;

DROP VIEW IF EXISTS `CourseEnrollmentSummary`;

CREATE VIEW
    `CourseEnrollmentSummary` AS
SELECT
    course.name,
    COUNT(enrollment.id) AS enrollment_count
FROM
    course
    INNER JOIN enrollment ON course.id = enrollment.course_id
GROUP BY
    course.name;

-- Question 5
SELECT
    AVG(age) as AverageAgeByCity,
    city
FROM
    student
GROUP BY
    city;

-- Question 6
SELECT
    *
FROM
    instructor
WHERE
    id IN (
        SELECT
            instructor_id
        FROM
            course
        WHERE
            credits > 4
    );

SELECT
    student.firstname,
    student.lastname,
    course.name as CourseName
FROM
    student
    LEFT JOIN enrollment ON student.id = enrollment.student_id
    LEFT JOIN course ON enrollment.course_id = course.id;

-- Question 7
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    course.name as CourseName,
    MAX(enrollment.grade) as HighestGrade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    CourseName;

-- Question 8
SELECT
    course.name as CourseName,
    COUNT(*) as EnrollmentCount
FROM
    enrollment
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    course_id
HAVING
    COUNT(*) > 1;

-- Question 9
WITH
    converted_grades as (
        SELECT
            course.id,
            course.instructor_id,
            grade,
            CASE
                WHEN grade = 'A' THEN 4
                WHEN grade = 'B' THEN 3
                WHEN grade = 'C' THEN 2
                ELSE 0
            END AS numeric_grade
        FROM
            enrollment
            JOIN course ON enrollment.course_id = course.id
    )
SELECT
    instructor.name,
    AVG(numeric_grade) AS AverageGrades
FROM
    converted_grades
    JOIN instructor ON converted_grades.instructor_id = instructor.id
GROUP BY
    instructor_id;

-- Question 10
SELECT DISTINCT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
WHERE
    EXISTS (
        SELECT
            *
        FROM
            enrollment enrollment1
            JOIN course course1 ON enrollment1.course_id = course1.id
        WHERE
            student.id = enrollment1.student_id
            AND EXISTS (
                SELECT
                    *
                FROM
                    enrollment enrollment2
                    JOIN course course2 ON enrollment2.course_id = course2.id
                WHERE
                    student.id = enrollment2.student_id
                    AND course1.instructor_id = course2.instructor_id
                    AND course1.id != course2.id
            )
    );

-- Question 11
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as Name,
    'Student' as label
FROM
    student
UNION ALL
SELECT
    name,
    'Instructor' as label
FROM
    instructor;

-- Question 12
SELECT
    course_id,
    SUM(
        CASE
            WHEN grade IN ('A', 'B') THEN 1
            ELSE 0
        END
    ) AS passed_students,
    SUM(
        CASE
            WHEN grade IN ('C') THEN 1
            ELSE 0
        END
    ) AS failed_students
FROM
    enrollment
GROUP BY
    course_id;

-- Question 13
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    enrollment_date,
    course.name
FROM
    student
    JOIN enrollment ON student.id = enrollment.student_id
    JOIN course ON enrollment.course_id = course.id
WHERE
    enrollment_date = (
        SELECT
            MAX(enrollment_date)
        FROM
            enrollment
        WHERE
            student_id = student.id
    );

SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
WHERE
    enrollment.grade = 'A';

-- Question 4
SELECT
    student.firstname,
    student.lastname,
    course.name,
    enrollment.grade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id;

DROP VIEW IF EXISTS `CourseEnrollmentSummary`;

CREATE VIEW
    `CourseEnrollmentSummary` AS
SELECT
    course.name,
    COUNT(enrollment.id) AS enrollment_count
FROM
    course
    INNER JOIN enrollment ON course.id = enrollment.course_id
GROUP BY
    course.name;

-- Question 5
SELECT
    AVG(age) as AverageAgeByCity,
    city
FROM
    student
GROUP BY
    city;

-- Question 6
SELECT
    *
FROM
    instructor
WHERE
    id IN (
        SELECT
            instructor_id
        FROM
            course
        WHERE
            credits > 4
    );

SELECT
    student.firstname,
    student.lastname,
    course.name as CourseName
FROM
    student
    LEFT JOIN enrollment ON student.id = enrollment.student_id
    LEFT JOIN course ON enrollment.course_id = course.id;

-- Question 7
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    course.name as CourseName,
    MAX(enrollment.grade) as HighestGrade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    CourseName;

-- Question 8
SELECT
    course.name as CourseName,
    COUNT(*) as EnrollmentCount
FROM
    enrollment
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    course_id
HAVING
    COUNT(*) > 1;

-- Question 9
WITH
    converted_grades as (
        SELECT
            course.id,
            course.instructor_id,
            grade,
            CASE
                WHEN grade = 'A' THEN 4
                WHEN grade = 'B' THEN 3
                WHEN grade = 'C' THEN 2
                ELSE 0
            END AS numeric_grade
        FROM
            enrollment
            JOIN course ON enrollment.course_id = course.id
    )
SELECT
    instructor.name,
    AVG(numeric_grade) AS AverageGrades
FROM
    converted_grades
    JOIN instructor ON converted_grades.instructor_id = instructor.id
GROUP BY
    instructor_id;

-- Question 10
SELECT DISTINCT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
WHERE
    EXISTS (
        SELECT
            *
        FROM
            enrollment enrollment1
            JOIN course course1 ON enrollment1.course_id = course1.id
        WHERE
            student.id = enrollment1.student_id
            AND EXISTS (
                SELECT
                    *
                FROM
                    enrollment enrollment2
                    JOIN course course2 ON enrollment2.course_id = course2.id
                WHERE
                    student.id = enrollment2.student_id
                    AND course1.instructor_id = course2.instructor_id
                    AND course1.id != course2.id
            )
    );

-- Question 11
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as Name,
    'Student' as label
FROM
    student
UNION ALL
SELECT
    name,
    'Instructor' as label
FROM
    instructor;

-- Question 12
SELECT
    course_id,
    SUM(
        CASE
            WHEN grade IN ('A', 'B') THEN 1
            ELSE 0
        END
    ) AS passed_students,
    SUM(
        CASE
            WHEN grade IN ('C') THEN 1
            ELSE 0
        END
    ) AS failed_students
FROM
    enrollment
GROUP BY
    course_id;

-- Question 13
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    enrollment_date,
    course.name
FROM
    student
    JOIN enrollment ON student.id = enrollment.student_id
    JOIN course ON enrollment.course_id = course.id
WHERE
    enrollment_date = (
        SELECT
            MAX(enrollment_date)
        FROM
            enrollment
        WHERE
            student_id = student.id
    );