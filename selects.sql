SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
WHERE
    enrollment.grade = 'A';

-- Question 4
SELECT
    student.firstname,
    student.lastname,
    course.name,
    enrollment.grade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id;

DROP VIEW IF EXISTS `CourseEnrollmentSummary`;

CREATE VIEW
    `CourseEnrollmentSummary` AS
SELECT
    course.name,
    COUNT(enrollment.id) AS enrollment_count
FROM
    course
    INNER JOIN enrollment ON course.id = enrollment.course_id
GROUP BY
    course.name;

-- Question 5
SELECT
    AVG(age) as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    city
FROM
    student
GROUP BY
    city;

-- Question 6
SELECT
    *
FROM
    instructor
WHERE
    id IN (
        SELECT
            instructor_id
        FROM
            course
        WHERE
            credits > 4
    );

SELECT
    student.firstname,
    student.lastname,
    course.name as CourseName
FROM
    student
    LEFT JOIN enrollment ON student.id = enrollment.student_id
    LEFT JOIN course ON enrollment.course_id = course.id;

-- Question 7
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    course.name as <PERSON><PERSON><PERSON>,
    MAX(enrollment.grade) as HighestGrade
FROM
    student
    INNER JOIN enrollment ON student.id = enrollment.student_id
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    CourseName;

-- Question 8
SELECT
    course.name as CourseName,
    COUNT(*) as EnrollmentCount
FROM
    enrollment
    INNER JOIN course ON enrollment.course_id = course.id
GROUP BY
    course_id
HAVING
    COUNT(*) > 1;

-- Question 9
WITH
    converted_grades as (
        SELECT
            course.id,
            course.instructor_id,
            grade,
            CASE
                WHEN grade = 'A' THEN 4
                WHEN grade = 'B' THEN 3
                WHEN grade = 'C' THEN 2
                ELSE 0
            END AS numeric_grade
        FROM
            enrollment
            JOIN course ON enrollment.course_id = course.id
    )
SELECT
    instructor.name,
    AVG(numeric_grade) AS AverageGrades
FROM
    converted_grades
    JOIN instructor ON converted_grades.instructor_id = instructor.id
GROUP BY
    instructor_id;

-- Question 10
SELECT DISTINCT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName
FROM
    student
WHERE
    EXISTS (
        SELECT
            *
        FROM
            enrollment enrollment1
            JOIN course course1 ON enrollment1.course_id = course1.id
        WHERE
            student.id = enrollment1.student_id
            AND EXISTS (
                SELECT
                    *
                FROM
                    enrollment enrollment2
                    JOIN course course2 ON enrollment2.course_id = course2.id
                WHERE
                    student.id = enrollment2.student_id
                    AND course1.instructor_id = course2.instructor_id
                    AND course1.id != course2.id
            )
    );

-- Question 11
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as Name,
    'Student' as label
FROM
    student
UNION ALL
SELECT
    name,
    'Instructor' as label
FROM
    instructor;

-- Question 12
SELECT
    course_id,
    SUM(
        CASE
            WHEN grade IN ('A', 'B') THEN 1
            ELSE 0
        END
    ) AS passed_students,
    SUM(
        CASE
            WHEN grade IN ('C') THEN 1
            ELSE 0
        END
    ) AS failed_students
FROM
    enrollment
GROUP BY
    course_id;

-- Question 13
SELECT
    CONCAT (student.firstname, ' ', student.lastname) as StudentName,
    enrollment_date,
    course.name
FROM
    student
    JOIN enrollment ON student.id = enrollment.student_id
    JOIN course ON enrollment.course_id = course.id
WHERE
    enrollment_date = (
        SELECT
            MAX(enrollment_date)
        FROM
            enrollment
        WHERE
            student_id = student.id
    );