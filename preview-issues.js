#!/usr/bin/env node

/**
 * Preview GitHub Issues
 * 
 * This script shows a preview of all the issues that will be created,
 * including their titles, labels, and a summary.
 */

const { applicationLayerIssues } = require('./github-issues-data');

function previewIssues() {
  console.log('GitHub Issues Preview');
  console.log('='.repeat(50));
  console.log(`Total issues to create: ${applicationLayerIssues.length}\n`);

  applicationLayerIssues.forEach((issue, index) => {
    console.log(`${index + 1}. ${issue.title}`);
    
    if (issue.labels && issue.labels.length > 0) {
      console.log(`   Labels: ${issue.labels.join(', ')}`);
    }
    
    // Show first line of description
    const firstLine = issue.body.split('\n').find(line => line.trim() && !line.startsWith('#'));
    if (firstLine) {
      console.log(`   Description: ${firstLine.trim()}`);
    }
    
    console.log('');
  });

  // Summary by category
  console.log('\nSummary by Category:');
  console.log('-'.repeat(30));
  
  const categories = {};
  applicationLayerIssues.forEach(issue => {
    const category = issue.title.match(/\[([^\]]+)\]/)?.[1] || 'Other';
    categories[category] = (categories[category] || 0) + 1;
  });

  Object.entries(categories).forEach(([category, count]) => {
    console.log(`${category}: ${count} issues`);
  });

  // Summary by labels
  console.log('\nSummary by Labels:');
  console.log('-'.repeat(30));
  
  const labelCounts = {};
  applicationLayerIssues.forEach(issue => {
    if (issue.labels) {
      issue.labels.forEach(label => {
        labelCounts[label] = (labelCounts[label] || 0) + 1;
      });
    }
  });

  Object.entries(labelCounts)
    .sort(([,a], [,b]) => b - a)
    .forEach(([label, count]) => {
      console.log(`${label}: ${count} issues`);
    });
}

if (require.main === module) {
  previewIssues();
}

module.exports = { previewIssues };
