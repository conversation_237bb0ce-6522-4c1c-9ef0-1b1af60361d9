// GitHub Issues Data for Application Layer
// This file contains all the issues to be created in bulk

const applicationLayerIssues = [
  {
    title: "[Application] Common | CommandBus Interface",
    labels: ["application", "common", "interface"],
    body: `## Description

Introduce a common CommandBus interface that defines the contract for executing application-layer commands. This interface is used by all bounded contexts (e.g., session) and provides a consistent execution surface.

## Requirements

**Location:**
- \`application/common/ports/\`

**Files:**
- \`CommandBus.ts\`

### CommandBus.ts

Export interface:

\`\`\`typescript
export interface CommandBus {
  execute(command: DomainCommand): Promise<CommandResult>;
}
\`\`\`

- \`DomainCommand\` comes from the domain layer.
- \`CommandResult\` reuses \`application/common/dto/Results.ts\`.

## Test Plan

Create \`application/common/ports/__tests__/command-bus.spec.ts\`:

- **type_only_contract**: Compile-time test (dummy implementation satisfies interface).
- **result_type_enforced**: Returning wrong type fails compilation.

## Acceptance Criteria

- [ ] File exists at specified path.
- [ ] Interface exported via \`@/application/common/ports\`.
- [ ] Tests pass in CI.`
  },
  {
    title: "[Application] Common | EventBus Interface + InMemory Implementation",
    labels: ["application", "common", "interface", "implementation"],
    body: `## Description

Provide a reusable EventBus interface for publishing domain events asynchronously, and an in-memory implementation for testing and development. The bus supports multiple handlers and isolates failures.

## Requirements

**Location:**
- \`application/common/ports/\`
- \`application/common/events/\`

**Files:**
- \`EventBus.ts\`
- \`InMemoryEventBus.ts\`

### EventBus.ts

Export interface:

\`\`\`typescript
export interface EventBus {
  publish(events: readonly DomainEvent[]): Promise<void>;
}
\`\`\`

### InMemoryEventBus.ts

Implementation with:
- \`addHandler(handler: (event: DomainEvent) => Promise<void>): void\`
- \`publish(events)\` → invokes each handler sequentially, catches errors, logs them.
- Async, non-blocking of command path.

## Test Plan

Create \`application/common/events/__tests__/in-memory-event-bus.spec.ts\`:

- **publishes_to_all_handlers**: Both handlers called for the same event.
- **isolates_handler_failure**: One handler throws → others still invoked.
- **supports_multiple_events**: Batch delivered in order.

## Acceptance Criteria

- [ ] Files exist at specified paths.
- [ ] Interface exported via \`@/application/common/ports\`.
- [ ] InMemory implementation available for unit tests.
- [ ] Tests pass in CI.`
  },
  {
    title: "[Application] Common | TaskBroker Interface",
    labels: ["application", "common", "interface"],
    body: `## Description

Introduce a TaskBroker application-layer port used to enqueue commands for serialized, resilient execution when sessions are live. The broker is a thin abstraction over infrastructure (e.g., BullMQ), providing:

- Per-session ordering via groupingKey (typically sessionId)
- Delayed scheduling for timers/autopilot
- Priority lanes for urgent ops (e.g., lock, return-all)
- Idempotency hints via dedupeKey (typically commandId)

This port is infrastructure-agnostic and will be implemented in the infra layer. Application code must not import infra libraries directly.

## Requirements

**Location:**
- \`application/common/ports/\`

**Files:**
- \`TaskBroker.ts\`
- \`index.ts\` (re-export from application/common/ports)

### TaskBroker.ts

Export interface:

\`\`\`typescript
export interface TaskBroker {
  enqueue(
    command: DomainCommand,
    opts?: {
      groupingKey?: string;   // required for per-session FIFO; usually the sessionId
      delayMs?: number;       // schedule for future execution
      priority?: number;      // lower number = higher priority (infra-dependent)
      dedupeKey?: string;     // idempotency key; usually command.commandId
      metadata?: {
        correlationId?: string; // for observability only; broker need not use it
        source?: 'API' | 'SCHEDULER' | 'WEBHOOK'; // provenance tag for logs/metrics
      };
    }
  ): Promise<void>;
}
\`\`\`

**Notes:**
- \`groupingKey\` should be provided by callers for state-mutating session commands to ensure single-writer semantics per session.
- \`delayMs\` will be used by the infra implementation to schedule the job (e.g., delayed job).
- \`priority\` is advisory; actual semantics depend on the infra adapter.
- \`dedupeKey\` allows the adapter to drop duplicates before execution when supported.
- The interface must not leak infra-specific types.

## Test Plan

Create \`application/common/ports/__tests__/task-broker.spec.ts\`:

- **type_contract_exports_interface**: Import TaskBroker and ensure a dummy class implementing it type-checks.
- **options_are_optional**: Call enqueue(command) with no opts and with each combination of opts fields; compile-time checks should pass.
- **does_not_leak_infra_types**: Verify file has no imports from infra packages (e.g., bullmq, redis, kafkajs) via a simple lint rule or comment in test.
- **doc_examples_compile**: Inline the usage snippets in a .ts test file to ensure they type-check against the interface.

## Acceptance Criteria

- [ ] TaskBroker.ts and index.ts exist at specified paths.
- [ ] Interface matches the specified signature and does not import any infra-specific libraries.
- [ ] Re-export available via \`@/application/common/ports\`.
- [ ] Unit tests compile and pass in CI (type-contract tests included).
- [ ] Short JSDoc comments included to guide callers on groupingKey, delayMs, priority, dedupeKey, and metadata usage.`
  },
  {
    title: "[Application] Common | Scheduler Interface",
    labels: ["application", "common", "interface"],
    body: `## Description

Introduce a Scheduler application-layer port for time-based orchestration. The scheduler allows process managers (autopilot) and APIs to schedule a domain command to execute at a specific future time. The infrastructure (e.g., BullMQ delayed jobs) will implement this port. Scheduled commands must re-enter the same per-session serialized pipeline (via TaskBroker) to preserve ordering with ad-hoc commands.

## Requirements

**Location:**
- \`application/common/ports/\`

**Files:**
- \`Scheduler.ts\`
- \`index.ts\` (re-export from application/common/ports)

### Scheduler.ts

Export interface:

\`\`\`typescript
export interface Scheduler {
  /**
   * Schedule a domain command to run at a specific time.
   * Implementations MUST ensure the command will be enqueued back
   * through the application's TaskBroker using the same groupingKey,
   * so ordering with ad-hoc commands is preserved.
   */
  schedule(
    command: DomainCommand,
    runAt: Date,
    groupingKey: string, // typically the sessionId
    opts?: {
      dedupeKey?: string; // usually command.commandId
      priority?: number;  // advisory; infra-specific
      metadata?: { correlationId?: string; source?: 'SCHEDULER' | 'API' | 'WEBHOOK' }
    }
  ): Promise<void>;
}
\`\`\`

**Notes:**
- \`groupingKey\` is required to maintain per-session FIFO ordering.
- Implementations should store enough info to survive restarts (infra concern).
- \`dedupeKey\` prevents duplicate schedules for the same logical operation.
- The interface must not import infra packages.

## Test Plan

Create \`application/common/ports/__tests__/scheduler.spec.ts\`:

- **type_contract_exports_interface**: Import Scheduler and ensure a dummy class implementing it type-checks.
- **requires_groupingKey**: Compile-time: calling schedule(cmd, runAt) without groupingKey fails type-check.
- **accepts_optional_opts**: Calls with/without opts compile; all optional fields are optional.
- **does_not_leak_infra_types**: Lint or static check that Scheduler.ts has no imports from infra libraries.
- **doc_example_compiles**: Copy the usage snippet into the test file to ensure it type-checks.

## Acceptance Criteria

- [ ] Scheduler.ts and index.ts exist at specified paths.
- [ ] Interface matches the specified signature and contains guidance comments.
- [ ] No infra-specific imports (e.g., bullmq, redis) in the file.
- [ ] Re-export available via \`@/application/common/ports\`.
- [ ] Unit tests compile and pass in CI.`
  },
  {
    title: "[Application] Common | Repository Base Contract",
    labels: ["application", "common", "repository"],
    body: `## Description

Provide a minimal, reusable Repository base contract that bounded contexts can extend (e.g., SessionRepository). Repositories operate on primitive DTOs (serialized forms of aggregates) to keep the application layer decoupled from persistence and the domain object graph.

## Requirements

**Location:**
- \`application/common/ports/\`

**Files:**
- \`Repository.ts\`
- \`index.ts\` (re-export from application/common/ports)

### Repository.ts

Export generic base types for repositories operating on primitives:

\`\`\`typescript
// Primitive DTO contract that repositories persist/retrieve.
export interface Repository<TPrimitives, TId extends string = string> {
  findById(id: TId): Promise<TPrimitives | null>;
  save(dto: TPrimitives): Promise<void>;
}

// Optional: typed error contracts to standardize infra error mapping.
export class RepositoryError extends Error {
  constructor(message: string, public readonly ctx?: Record<string, unknown>) {
    super(message);
    this.name = 'RepositoryError';
  }
}

export class ConcurrencyError extends RepositoryError {
  constructor(message = 'Optimistic concurrency conflict', ctx?: Record<string, unknown>) {
    super(message, ctx);
    this.name = 'ConcurrencyError';
  }
}

export class NotFoundError extends RepositoryError {
  constructor(message = 'Entity not found', ctx?: Record<string, unknown>) {
    super(message, ctx);
    this.name = 'NotFoundError';
  }
}
\`\`\`

**Notes:**
- Keep the contract small and stable: findById, save.
- Work exclusively with primitive DTOs; no domain objects here.
- Error types are optional but recommended for consistent error mapping in the app layer.

## Test Plan

Create \`application/common/ports/__tests__/repository-base.spec.ts\`:

- **type_contract_minimal**: A dummy repository class implementing Repository<{ id: string; v: number }> compiles and can be instantiated.
- **findById_returns_null_when_missing**: In a dummy in-memory implementation, call findById('missing') → expect null.
- **save_and_find_roundtrip**: Save a DTO and then findById returns an equal but not same-reference object (defensive copy if applicable).
- **custom_id_type**: Implement Repository<MyDto, MyId> where type MyId = string & { __brand: 'SessionId' } compiles (verifies generic ID flexibility).
- **error_types_exported**: Import and instantiate RepositoryError, ConcurrencyError, NotFoundError; ensure .name is correct.

## Acceptance Criteria

- [ ] Files exist at specified paths.
- [ ] Repository generic interface exactly matches the specified signature.
- [ ] No domain objects or infra-specific imports appear in Repository.ts.
- [ ] Re-exports available via \`@/application/common/ports\`.
- [ ] Unit tests compile and pass in CI.`
  },
  {
    title: "[Application] Common | CommandValidator Service",
    labels: ["application", "common", "service", "validation"],
    body: `## Description

Introduce a reusable CommandValidator service that performs fail-fast structural validation on incoming DomainCommands before they reach handlers. The goal is to eliminate malformed inputs at the application boundary and keep domain code focused on business rules. Validation includes presence/shape checks, UUID format, timestamp sanity, and session-target checks for session-scoped commands.

## Requirements

**Location:**
- \`application/common/services/\`

**Files:**
- \`CommandValidator.ts\`
- \`__tests__/CommandValidator.spec.ts\`
- (re-export from) \`application/common/index.ts\`

### CommandValidator.ts

Export interface and default implementation:

\`\`\`typescript
export interface CommandValidator {
  ensureValid(command: DomainCommand): void;  // throws on invalid
}

export class DefaultCommandValidator implements CommandValidator {
  ensureValid(command: DomainCommand): void;
}
\`\`\`

**Validation rules** (throw Error with clear message including code prefix):

**Base presence:**
- command is object (VAL.COMMAND_NOT_OBJECT)
- command.kind non-empty string (VAL.KIND_REQUIRED)
- command.commandId present + UUID v4 (VAL.COMMAND_ID_INVALID)
- command.issuedAt is finite number, ≥ 0, and <= Date.now() + 60_000 to allow slight skew (VAL.ISSUED_AT_INVALID)

**Actor:**
- command.actor present (VAL.ACTOR_REQUIRED)
- command.actor.role in known roles (SYSTEM|HOST|CO_HOST|ADMIN|ATTENDEE) (VAL.ACTOR_ROLE_INVALID)

**Session-scoped commands:**
- If command payload includes a sessionId (most do), require non-empty string UUID v4 (VAL.SESSION_ID_INVALID)

**Expected version (optional):**
- If expectedVersion present, must be integer ≥ 0 (VAL.EXPECTED_VERSION_INVALID)

**Correlation/Causation (optional):**
- If provided, correlationId and causationId must be UUID v4 (VAL.CORRELATION_ID_INVALID, VAL.CAUSATION_ID_INVALID)

**Version field:**
- version (schema version) must be integer ≥ 1 if present (VAL.SCHEMA_VERSION_INVALID)

## Test Plan

Create \`application/common/services/__tests__/CommandValidator.spec.ts\`:

- **valid_minimal_command_passes**: Builds a minimal valid command and ensureValid does not throw.
- **rejects_non_object**: ensureValid(undefined as any) throws with code VAL.COMMAND_NOT_OBJECT.
- **rejects_missing_kind**: Missing/empty kind throws VAL.KIND_REQUIRED.
- **rejects_bad_commandId**: Non-UUID v4 commandId throws VAL.COMMAND_ID_INVALID.
- **rejects_bad_issuedAt**: NaN/negative or too-far-future timestamp throws VAL.ISSUED_AT_INVALID.
- **rejects_missing_actor**: Missing actor throws VAL.ACTOR_REQUIRED.
- **rejects_invalid_actor_role**: Unknown role throws VAL.ACTOR_ROLE_INVALID.
- **sessionId_must_be_uuid_when_present**: Payload with non-UUID sessionId throws VAL.SESSION_ID_INVALID.
- **expectedVersion_must_be_non_negative_int**: Floats/negative/NaN throw VAL.EXPECTED_VERSION_INVALID.
- **correlation_and_causation_ids_if_present_must_be_uuid**: Bad IDs throw respective codes.
- **schema_version_if_present_must_be_int_ge_1**: Bad values throw VAL.SCHEMA_VERSION_INVALID.
- **does_not_mutate_input**: Deep-freeze input; validator must not modify it.

## Acceptance Criteria

- [ ] DefaultCommandValidator enforces all rules above and throws Error with the specified code prefixes in the message.
- [ ] Export available via \`@/application/common\`.
- [ ] No infrastructure or domain persistence imports.
- [ ] All tests pass in CI (including failure case assertions on error messages).`
  },
  {
    title: "[Application] Common | IdempotencyLedger Service",
    labels: ["application", "common", "service", "idempotency"],
    body: `## Description

Introduce a reusable IdempotencyLedger service to guarantee that a DomainCommand identified by commandId is applied at most once by the application layer, even under retries or redeliveries (e.g., queue replays). The ledger is checked before executing domain logic and marked after a successful save + event publish. This prevents duplicate state changes and duplicate event emissions.

## Requirements

**Location:**
- \`application/common/services/\`

**Files:**
- \`IdempotencyLedger.ts\`
- \`__tests__/IdempotencyLedger.spec.ts\`
- (re-export from) \`application/common/index.ts\`

### IdempotencyLedger.ts

Export interface and default in-memory implementation:

\`\`\`typescript
export interface IdempotencyLedger {
  /**
   * Return true if the commandId has already been applied (and should be skipped).
   * This MUST be a fast, side-effect-free check.
   */
  isApplied(commandId: string): Promise<boolean>;

  /**
   * Mark commandId as applied. Must be called only after a successful
   * aggregate save and event publish to avoid false positives.
   */
  markApplied(commandId: string, opts?: { ttlMs?: number }): Promise<void>;
}

/**
 * MVP in-memory implementation suitable for unit tests and dev runs.
 * Production implementations should use a durable store (e.g., Redis) in infra.
 */
export class InMemoryIdempotencyLedger implements IdempotencyLedger {
  // constructor can accept clock injection for tests if desired
  constructor(opts?: { now?: () => number }) {}

  isApplied(commandId: string): Promise<boolean>;
  markApplied(commandId: string, opts?: { ttlMs?: number }): Promise<void>;
}
\`\`\`

**Behavior notes:**
- commandId must be treated as a case-sensitive key (UUID v4 strings).
- markApplied supports optional ttlMs for automatic expiry (defaults to 24h for MVP; value may be tuned later).
- In-memory impl may use a Map<string, number> storing expiresAtMs for each commandId, and GC expired entries on reads/writes.
- No external I/O in the default implementation; infra will supply a durable adapter later.

## Test Plan

Create \`application/common/services/__tests__/IdempotencyLedger.spec.ts\`:

- **returns_false_for_unknown_id**: isApplied('c-1') → false.
- **marks_and_reads_true**: markApplied('c-1') then isApplied('c-1') → true.
- **respects_ttl_expiry**: Use injected clock or fake timers: markApplied('c-1', { ttlMs: 50 }); advance time > 50ms; isApplied('c-1') → false.
- **idempotent_marking**: Calling markApplied('c-1') twice does not throw; isApplied('c-1') stays true.
- **case_sensitive_keys**: Mark 'C-1'; isApplied('c-1') remains false.
- **gc_on_read_write**: Add multiple entries with short TTLs; after expiry, reads/writes clean up internal map.

## Acceptance Criteria

- [ ] IdempotencyLedger.ts and tests exist at the specified paths.
- [ ] InMemoryIdempotencyLedger implements the exact interface and honors ttlMs.
- [ ] No infrastructure dependencies (no redis, bullmq, etc.).
- [ ] Re-exports available via \`@/application/common\`.
- [ ] All unit tests pass in CI.`
  },
  {
    title: "[Application] Common | OutboxService (Tx-Safe Event/Task Outbox)",
    labels: ["application", "common", "service", "outbox"],
    body: `## Description

Introduce an OutboxService to guarantee reliable delivery of side effects (event publishing and task enqueueing) after aggregate state is saved. The outbox pattern prevents the "saved state but lost message" problem by persisting outbox records in the same transaction as the aggregate write, and flushing them asynchronously. For MVP, define the application-layer contract and a simple in-memory implementation for tests/dev; infrastructure implementations (DB-backed) will come later.

## Requirements

**Location:**
- \`application/common/services/\`

**Files:**
- \`OutboxService.ts\`
- \`__tests__/OutboxService.spec.ts\`
- (re-export from) \`application/common/index.ts\`

### OutboxService.ts

Export types:

**OutboxRecord:**
- \`id: string\` (UUID v4)
- \`type: 'EVENT' | 'TASK'\`
- \`correlationId: string\`
- \`causationId?: string\`
- \`payload: Readonly<DomainEvent | DomainCommand>\`
- \`createdAt: number\` (server time ms)
- \`status: 'PENDING' | 'DELIVERED' | 'FAILED'\`
- \`attempts: number\`
- \`lastError?: string\`

**AppendOptions:**
- \`batchId?: string\` (to append multiple records under the same transactional batch)

Export interface:

\`\`\`typescript
export interface OutboxService {
  /**
   * Append records to the outbox within the caller's transaction boundary.
   * The implementation MUST be safe to call multiple times in the same tx.
   */
  append(records: ReadonlyArray<OutboxRecord>, opts?: AppendOptions): Promise<void>;

  /**
   * Flush a bounded number of PENDING outbox records, delivering each to the appropriate sink:
   * - EVENT → EventBus.publish([...])
   * - TASK  → TaskBroker.enqueue(...)
   * The implementation should mark records DELIVERED on success and FAILED with lastError on error.
   */
  flushOnce(limit?: number): Promise<{ delivered: number; failed: number }>;
}
\`\`\`

Export in-memory MVP implementation (dev/test only):

**InMemoryOutboxService:**
- Stores records in a local array/map.
- Requires EventBus and TaskBroker instances in its constructor.
- \`append(...)\` pushes records into memory.
- \`flushOnce(limit = 100)\`: Takes up to limit PENDING records (stable order by createdAt then id).

## Test Plan

Create \`application/common/services/__tests__/OutboxService.spec.ts\`:

- **append_and_flush_events_success**: Given one EVENT record and a spy EventBus, flushOnce() publishes event and marks record DELIVERED.
- **append_and_flush_tasks_success**: Given one TASK record and a spy TaskBroker, flushOnce() enqueues command and marks record DELIVERED.
- **flush_respects_limit_and_order**: With 3 records (older first), flushOnce(2) delivers the first two in order; the third remains PENDING.
- **failed_publish_marks_failed_and_keeps_error**: EventBus throws → record marked FAILED with lastError, attempts===1.
- **subsequent_flush_can_retry_failed**: On second flushOnce(), if handler succeeds, record transitions to DELIVERED.
- **idempotent_append_call**: Calling append() twice with the same record id does not create duplicates.
- **does_not_import_infra**: Static check that the file does not import redis, bullmq, etc.

## Acceptance Criteria

- [ ] Outbox types and interface exist and are exported via \`@/application/common\`.
- [ ] InMemoryOutboxService compiles, passes tests, and demonstrates the delivery flow to EventBus and TaskBroker.
- [ ] flushOnce(limit) behavior matches the spec (ordering, status transitions, error capture).
- [ ] No infrastructure dependencies are imported from the common module.
- [ ] All unit tests pass in CI.`
  },
  {
    title: "[Application] Common | ContextLogger (Structured Logging)",
    labels: ["application", "common", "logging", "observability"],
    body: `## Description

Introduce a ContextLogger utility that emits structured logs for application-layer operations. It standardizes keys (e.g., correlationId, commandId, sessionId, eventId, kind, actor.role) and provides helper methods to log command start/success/failure and event publication. This ensures consistent observability across all bounded contexts and allows easy querying in log backends.

## Requirements

**Location:**
- \`application/common/instrumentation/\`

**Files:**
- \`ContextLogger.ts\`
- \`__tests__/ContextLogger.spec.ts\`
- (re-export from) \`application/common/index.ts\`

### ContextLogger.ts

Export the public interface and a default implementation:

\`\`\`typescript
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogSink {
  log(level: LogLevel, message: string, fields?: Record<string, unknown>): void;
}

export interface ContextLogger {
  with(fields: Record<string, unknown>): ContextLogger;

  // Generic log
  log(level: LogLevel, message: string, fields?: Record<string, unknown>): void;

  // Command lifecycle helpers
  logCommandStart(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; actorRole?: string;
  }): void;

  logCommandSuccess(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; aggregateVersion?: number; durationMs?: number;
  }): void;

  logCommandFailure(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; error: unknown; durationMs?: number;
  }): void;

  // Event publishing helper
  logEventsPublished(fields: {
    correlationId?: string; sessionId?: string;
    eventCount: number; kinds?: readonly string[];
  }): void;
}

export class DefaultContextLogger implements ContextLogger { /* see notes */ }
\`\`\`

**Behavior & conventions:**
- **Immutable context**: with(fields) returns a new logger instance that merges the extra fields as defaults for subsequent logs (does not mutate original).
- **Standard keys** included in every log when available: correlationId, commandId, eventId (when relevant), sessionId, roundId, roomId, kind (command or event kind), actor.role.
- **DefaultContextLogger** delegates to a provided LogSink (constructor param). The sink is the only place that touches console, Pino, Winston, etc. (keeps common module infra-agnostic).
- **Messages** are short action verbs; context fields carry the details.
- No external imports from infra packages.

## Test Plan

Create \`application/common/instrumentation/__tests__/ContextLogger.spec.ts\`:

- **with_returns_new_logger_and_merges_fields**: logger.with({ a: 1 }) returns a new instance; original unchanged; subsequent calls include a:1.
- **log_includes_standard_context_keys**: Using logCommandStart with fields results in sink receiving a log with keys: correlationId, commandId, kind, sessionId, actor.role (when provided).
- **success_and_failure_emit_expected_fields**: logCommandSuccess includes aggregateVersion and durationMs; logCommandFailure includes error and durationMs.
- **events_published_includes_counts_and_kinds**: Verify sink was called with eventCount and kinds[].
- **no_infra_imports**: Static check/lint that file has no imports from logging libraries.
- **immutability**: Calling with() does not affect logs emitted by previous logger instance.

## Acceptance Criteria

- [ ] ContextLogger interface and DefaultContextLogger exist and compile.
- [ ] No direct dependency on specific logging frameworks in the common module.
- [ ] JSDoc usage examples present in the file.
- [ ] Tests pass in CI and confirm field presence, immutability, and sink delegation.`
  },
  {
    title: "[Application] SessionCommandHandler Routing Setup",
    labels: ["application", "session", "handler", "routing"],
    body: `## Description

Implement the SessionCommandHandler as the central orchestrator for all session-related commands. It will perform validation, route commands to the appropriate category handler (lifecycle, rounds, participants, rooms, host), manage aggregate loading/saving, and publish resulting events via the EventBus.

## Requirements

**Location:**
- \`application/session/handlers/SessionCommandHandler.ts\`

**Dependencies:**
- \`ports/SessionRepository\` (to load/save sessions)
- \`ports/EventBus\` (to publish domain events)
- \`services/CommandValidator\` (structural + invariant checks)
- \`services/IdempotencyLedger\` (prevent replays)

**Responsibilities:**

\`handle(command: DomainCommand): Promise<Result>\`

1. Validate structure with CommandValidator.
2. Check deduplication with IdempotencyLedger.
3. Extract sessionId (if applicable).
4. Route by command.kind to specialized category handlers.
5. Save updated aggregate via SessionRepository.
6. Publish collected domain events via EventBus.
7. Return Results.Success or Results.Failure.

**Routing Categories:**
- \`lifecycle.ts\` – Start/Pause/Resume/Complete/Cancel
- \`participants.ts\` – Add/Join/Leave/Seat/Restore
- \`rooms.ts\` – CreateBreakout/Assign/Release/Rotate
- \`rounds.ts\` – AddRound/StartRound/EndCurrentRound
- \`host.ts\` – SetHost

**Error Handling:**
- Convert thrown domain errors into Results.Failure.
- Attach correlation/causation IDs to logs and responses.

## Test Plan

Create \`application/session/__tests__/session-command-handler.spec.ts\`:

- **happy_path_lifecycle**: Given StartSessionCommand, repository returns scheduled session → expect SessionStartedEvent published, result.success true.
- **invalid_command_structure**: Given malformed command object → expect Results.Failure with validation error.
- **idempotent_duplicate**: Given duplicate commandId → IdempotencyLedger prevents reprocessing → expect Results.Success with no new events.
- **aggregate_not_found**: Given command referencing missing session → expect failure with SessionNotFoundError.
- **event_bus_failure_isolated**: Simulate EventBus.publishEvents throwing → command still returns success, error logged.

## Acceptance Criteria

- [ ] SessionCommandHandler routes all supported commands via category handlers.
- [ ] Validates structure and deduplication before execution.
- [ ] Saves updated aggregate state with correct version.
- [ ] Publishes all resulting domain events asynchronously.
- [ ] Returns consistent Results objects.
- [ ] Test suite covers success, failure, idempotency, and integration paths.`
  },
  {
    title: "[Application] Session | Routing: lifecycle.ts",
    labels: ["application", "session", "routing", "lifecycle"],
    body: `## Description

Implement the lifecycle routing module that handles session lifecycle commands by delegating to the Session aggregate. This module is invoked by SessionCommandHandler and encapsulates the orchestration for lifecycle transitions. It must load the aggregate (via repository), call the appropriate domain method, collect uncommitted events, persist the new state, and return the result.

**Supported commands for MVP:**
- CreateSession
- StartSession
- PauseSession
- ResumeSession
- CompleteSession
- CancelSession

## Requirements

**Location:**
- \`application/session/handlers/routing/lifecycle.ts\`

**Files:**
- \`lifecycle.ts\`
- \`__tests__/lifecycle.spec.ts\`

### lifecycle.ts

Export a narrow API used by SessionCommandHandler:

\`\`\`typescript
handleCreateSession(command, deps): Promise<ResultSuccess | ResultFailure>
handleLifecycle(command, deps): Promise<ResultSuccess | ResultFailure>
\`\`\`

**Types:**
\`deps\` contains:
\`\`\`typescript
{
  repo: SessionRepository;
  validator: CommandValidator;
  // optional hooks for observability
  log?: ContextLogger;
  now?: () => number; // test seam for time-based fields
}
\`\`\`

**handleCreateSession:**
1. Validate command structure with validator.ensureValid(...).
2. Convert payload primitives to domain-friendly primitives only if needed by domain factory.
3. Invoke domain factory Session.create(...) (domain already implemented).
4. Extract uncommitted events from the new aggregate.
5. Persist via repo.save(session.toPrimitives()).
6. Return { success: true, events, aggregateVersion }.

**handleLifecycle:**
1. Validate command structure.
2. Extract sessionId (command.payload.sessionId).
3. Load existing session via repo.findById(sessionId); return failure if null.
4. Rehydrate with Session.fromPrimitives(dto).
5. Switch on command.kind and call appropriate session methods.
6. Collect events → repo.save(updated.toPrimitives()).
7. Return { success: true, events, aggregateVersion: updated.getVersion() }.

**Error handling:**
- If repo returns null: map to Results.Failure with code: 'APP.SESSION_NOT_FOUND' and ctx: { sessionId }.
- Domain violations (invalid transition) should be caught and mapped to Results.Failure with code: 'APP.DOMAIN_VIOLATION' and include .message.
- Unknown command.kind → Results.Failure with code: 'APP.UNSUPPORTED_COMMAND'.

## Test Plan

Create \`application/session/handlers/routing/__tests__/lifecycle.spec.ts\`:

- **create_session_happy_path**: Arrange: valid CreateSession command with required payload → Assert: result.success, events contain SessionCreated, aggregateVersion === 1.
- **start_session_happy_path**: Arrange: repo returns SCHEDULED session DTO → Assert: success, events include SessionStarted, repo.save called with updated state.
- **pause_resume_flow**: Arrange: RUNNING → PauseSession then ResumeSession → Assert: each call returns success and publishes SessionPaused/SessionResumed.
- **complete_session_happy_path**: Arrange: RUNNING → CompleteSession → Assert: success, SessionCompleted event present.
- **cancel_from_scheduled**: Arrange: SCHEDULED → CancelSession → Assert: success, SessionCanceled event present.
- **session_not_found**: Arrange: repo.findById returns null → Assert: failure code: 'APP.SESSION_NOT_FOUND'.
- **invalid_transition_fails**: Arrange: already RUNNING; invoke StartSession → Assert: failure code: 'APP.DOMAIN_VIOLATION'.
- **unsupported_command_kind**: Arrange: call handleLifecycle with a non-lifecycle kind → Assert: failure code: 'APP.UNSUPPORTED_COMMAND'.
- **observability_hooks_called**: With a spy ContextLogger, assert logCommandStart/Success called with expected fields and that durationMs is positive.

## Acceptance Criteria

- [ ] lifecycle.ts exposes handleCreateSession and handleLifecycle with the specified signatures.
- [ ] All specified lifecycle commands route correctly to the Session aggregate and return a Result with events and version.
- [ ] Proper failure modes for not-found, invalid transition, and unsupported commands are implemented with error codes.
- [ ] Repository usage matches the pattern: findById (when needed) → domain call → save.
- [ ] Optional observability hooks integrate without being mandatory.
- [ ] Unit tests cover all happy paths and failure cases; tests pass in CI.`
  }
];

module.exports = { applicationLayerIssues };
