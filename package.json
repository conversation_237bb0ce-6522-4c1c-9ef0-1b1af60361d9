{"name": "github-issues-bulk-creator", "version": "1.0.0", "description": "Bulk create GitHub issues for application layer requirements", "main": "bulk-create-github-issues.js", "scripts": {"create-issues": "node bulk-create-github-issues.js", "preview": "node preview-issues.js", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@octokit/rest": "^20.0.2", "dotenv": "^16.3.1"}, "devDependencies": {}, "keywords": ["github", "issues", "bulk", "automation", "octokit"], "author": "", "license": "MIT"}