-- - CHIMPAYE CREDO Thierry 25093664


-- - Entities:
-- --- Instructors (id, name)
-- --- Courses (id, title, instructor_id)
-- --- Students (id, name)
-- --- Assignments (id, course_id, deadline)
-- --- Enrollments (id, course_id, student_id)
-- --- Grades (id, assignment_id, student_id, value)


-- - CREATING DATABASE
CREATE DATABASE IF NOT EXISTS elearningdb;

USE elearningdb;

-- - CREATING TABLES
CREATE TABLE IF NOT EXISTS instructors (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100));
CREATE TABLE IF NOT EXISTS students (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100));
CREATE TABLE IF NOT EXISTS courses (id INT AUTO_INCREMENT PRIMARY KEY, title VARCHAR(255), instructor_id INT, FOREIG<PERSON> (instructor_id) REFERENCES instructors(id));
CREATE TABLE IF NOT EXISTS assignments (id INT AUTO_INCREMENT PRIMARY KEY, course_id INT, deadline TIMESTAMP, FOREIGN KEY (course_id) REFERENCES courses(id));
CREATE TABLE IF NOT EXISTS enrollments (id INT AUTO_INCREMENT PRIMARY KEY, course_id INT, student_id INT, FOREIGN KEY (course_id) REFERENCES courses(id), FOREIGN KEY (student_id) REFERENCES students(id));
CREATE TABLE IF NOT EXISTS grades (id INT AUTO_INCREMENT PRIMARY KEY, value DECIMAL, assignment_id INT, student_id INT, FOREIGN KEY (assignment_id) REFERENCES assignments(id), FOREIGN KEY (student_id) REFERENCES students(id));

-- - INSERTING DATA
INSERT INTO instructors (name) VALUES ('Johnson Dukoby');
INSERT INTO instructors (name) VALUES ('Anderson Mokili');
INSERT INTO instructors (name) VALUES ('Sarah Martinez');
INSERT INTO instructors (name) VALUES ('Michael Chen');
INSERT INTO instructors (name) VALUES ('Emily Thompson');

INSERT INTO students (name) VALUES ('John Smith');
INSERT INTO students (name) VALUES ('Maria Garcia');
INSERT INTO students (name) VALUES ('David Kim');
INSERT INTO students (name) VALUES ('Lisa Wong');
INSERT INTO students (name) VALUES ('James Brown');

INSERT INTO courses (title, instructor_id) VALUES ('Introduction to Programming', 1);
INSERT INTO courses (title, instructor_id) VALUES ('Database Management', 2);
INSERT INTO courses (title, instructor_id) VALUES ('Web Development', 3);
INSERT INTO courses (title, instructor_id) VALUES ('Data Structures', 4);
INSERT INTO courses (title, instructor_id) VALUES ('Machine Learning', 5);

INSERT INTO enrollments (course_id, student_id) VALUES (1, 1);
INSERT INTO enrollments (course_id, student_id) VALUES (2, 2);
INSERT INTO enrollments (course_id, student_id) VALUES (3, 3);
INSERT INTO enrollments (course_id, student_id) VALUES (4, 4);
INSERT INTO enrollments (course_id, student_id) VALUES (5, 5);

INSERT INTO assignments (course_id, deadline) VALUES (1, '2024-01-15 23:59:59');
INSERT INTO assignments (course_id, deadline) VALUES (2, '2024-01-20 23:59:59');
INSERT INTO assignments (course_id, deadline) VALUES (3, '2024-01-25 23:59:59');
INSERT INTO assignments (course_id, deadline) VALUES (4, '2024-01-30 23:59:59');
INSERT INTO assignments (course_id, deadline) VALUES (5, '2024-02-05 23:59:59');

INSERT INTO grades (value, assignment_id, student_id) VALUES (85.5, 1, 1);
INSERT INTO grades (value, assignment_id, student_id) VALUES (92.0, 2, 2);
INSERT INTO grades (value, assignment_id, student_id) VALUES (78.5, 3, 3);
INSERT INTO grades (value, assignment_id, student_id) VALUES (88.0, 4, 4);
INSERT INTO grades (value, assignment_id, student_id) VALUES (95.5, 5, 5);
