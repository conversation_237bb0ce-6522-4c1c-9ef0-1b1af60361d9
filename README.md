# GitHub Issues Bulk Creator

This tool allows you to bulk create GitHub issues from structured data using the GitHub REST API (Octokit). It's specifically designed for creating application layer requirements as GitHub issues with proper markdown formatting.

## Features

- ✅ Bulk create multiple GitHub issues at once
- ✅ Proper markdown formatting for issue bodies
- ✅ Support for labels and structured content
- ✅ Rate limiting protection with configurable delays
- ✅ Error handling and retry logic
- ✅ Detailed progress reporting and summary
- ✅ Confirmation prompt before creating issues

## Prerequisites

1. **Node.js** (version 14 or higher)
2. **GitHub Personal Access Token** with appropriate permissions
3. **Repository access** (read/write permissions for issues)

## Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Create GitHub Token

1. Go to [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)
2. Click "Generate new token (classic)"
3. Give it a descriptive name (e.g., "Issues Bulk Creator")
4. Select the following scopes:
   - For **public repositories**: `public_repo`
   - For **private repositories**: `repo` (full control)
5. Click "Generate token"
6. **Copy the token immediately** (you won't be able to see it again)

### 3. Configure Environment

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` and add your GitHub token:
   ```
   GITHUB_TOKEN=your_actual_github_token_here
   ```

### 4. Configure Repository

Edit `bulk-create-github-issues.js` and update these constants:

```javascript
const REPO_OWNER = 'your-username';  // Your GitHub username or organization
const REPO_NAME = 'your-repo-name';  // Your repository name
```

## Usage

### Basic Usage

```bash
npm run create-issues
```

Or directly:

```bash
node bulk-create-github-issues.js
```

### What Happens

1. **Validation**: The script validates your configuration and GitHub token
2. **Preview**: Shows you how many issues will be created
3. **Confirmation**: Asks for your confirmation before proceeding
4. **Creation**: Creates issues one by one with delays to respect rate limits
5. **Summary**: Provides a detailed report of successes and failures

### Example Output

```
✅ Configuration validated
Found 8 issues to create

Do you want to create 8 issues in your-username/your-repo? (y/N): y

Starting bulk creation of 8 issues...
Repository: your-username/your-repo
Delay between issues: 1000ms

Creating issue: [Application] Common | CommandBus Interface
✅ Created issue #123: [Application] Common | CommandBus Interface
Waiting 1000ms before next issue...

Creating issue: [Application] Common | EventBus Interface + InMemory Implementation
✅ Created issue #124: [Application] Common | EventBus Interface + InMemory Implementation
...

==================================================
BULK CREATION SUMMARY
==================================================
Total issues to create: 8
Successfully created: 8
Failed: 0

Successfully created issues:
- #123: [Application] Common | CommandBus Interface
  URL: https://github.com/your-username/your-repo/issues/123
- #124: [Application] Common | EventBus Interface + InMemory Implementation
  URL: https://github.com/your-username/your-repo/issues/124
...
```

## Customization

### Adding More Issues

Edit `github-issues-data.js` to add more issues to the `applicationLayerIssues` array:

```javascript
const applicationLayerIssues = [
  {
    title: "[Your Category] Your Issue Title",
    labels: ["label1", "label2", "label3"],
    body: `## Description

Your issue description in markdown format.

## Requirements

- Requirement 1
- Requirement 2

## Acceptance Criteria

- [ ] Criterion 1
- [ ] Criterion 2`
  },
  // ... more issues
];
```

### Adjusting Rate Limiting

You can modify the delay between issue creations in the script:

```javascript
await createAllIssues(applicationLayerIssues, 2000); // 2 second delay
```

### Issue Structure

Each issue object should have:

- **title** (required): The issue title
- **body** (required): The issue description in markdown
- **labels** (optional): Array of label names

## File Structure

```
.
├── bulk-create-github-issues.js    # Main script
├── github-issues-data.js           # Issue data definitions
├── package.json                    # Dependencies
├── .env.example                    # Environment template
├── .env                           # Your environment (create this)
└── README.md                      # This file
```

## Troubleshooting

### Common Issues

1. **"GITHUB_TOKEN environment variable is required"**
   - Make sure you created the `.env` file with your token

2. **"Please update REPO_OWNER and REPO_NAME"**
   - Edit the script and set the correct repository details

3. **"Bad credentials" or 403 errors**
   - Check that your GitHub token is valid and has the right permissions
   - Make sure the token hasn't expired

4. **Rate limiting errors**
   - Increase the delay between requests
   - GitHub allows 5000 requests per hour for authenticated users

5. **"Not Found" errors**
   - Verify the repository owner and name are correct
   - Ensure you have write access to the repository

### GitHub API Limits

- **Rate Limit**: 5,000 requests per hour for authenticated users
- **Secondary Rate Limit**: No more than 100 requests per minute
- The script includes a 1-second delay by default to stay well within limits

## Security Notes

- Never commit your `.env` file to version control
- Keep your GitHub token secure and rotate it regularly
- Use the minimum required permissions for your token

## Contributing

Feel free to submit issues and enhancement requests!

## License

MIT License - see the code for details.
