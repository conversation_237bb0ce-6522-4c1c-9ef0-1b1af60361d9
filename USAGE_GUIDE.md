# GitHub Issues Bulk Creator - Usage Guide

This guide will walk you through setting up and using the GitHub Issues Bulk Creator to create all your application layer issues.

## Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Preview Issues

Before creating anything, see what will be created:

```bash
npm run preview
```

This shows you:
- Total number of issues (currently **11 issues**)
- Each issue title, labels, and description preview
- Summary by category and labels

### 3. Setup GitHub Token

1. Go to [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)
2. Create a new token with `repo` scope (for private repos) or `public_repo` (for public repos)
3. Copy the token

### 4. Configure Environment

```bash
cp .env.example .env
```

Edit `.env` and add your token:
```
GITHUB_TOKEN=your_actual_github_token_here
```

### 5. Configure Repository

Edit `bulk-create-github-issues.js` and update:

```javascript
const REPO_OWNER = 'your-username';     // Your GitHub username
const REPO_NAME = 'your-repository';    // Your repository name
```

### 6. Create Issues

```bash
npm run create-issues
```

The script will:
- Validate your configuration
- Show you a summary
- Ask for confirmation
- Create issues one by one with delays
- Provide a detailed summary

## Current Issues Overview

The system is configured to create **11 application layer issues**:

### Common Layer (9 issues)
1. **CommandBus Interface** - Contract for executing commands
2. **EventBus Interface + InMemory Implementation** - Event publishing with handlers
3. **TaskBroker Interface** - Command queuing with ordering and scheduling
4. **Scheduler Interface** - Time-based command orchestration
5. **Repository Base Contract** - Generic repository pattern for DTOs
6. **CommandValidator Service** - Structural validation for commands
7. **IdempotencyLedger Service** - Prevent duplicate command execution
8. **OutboxService** - Transactional outbox pattern for reliable side effects
9. **ContextLogger** - Structured logging for observability

### Session Layer (2 issues)
10. **SessionCommandHandler Routing Setup** - Central command orchestrator
11. **Session Routing: lifecycle.ts** - Lifecycle command routing module

## Customization

### Adding More Issues

Edit `github-issues-data.js` and add to the `applicationLayerIssues` array:

```javascript
{
  title: "[Your Category] Your Issue Title",
  labels: ["label1", "label2"],
  body: `## Description
  
Your markdown description here...

## Requirements

- Requirement 1
- Requirement 2

## Acceptance Criteria

- [ ] Criterion 1
- [ ] Criterion 2`
}
```

### Issue Template Structure

Each issue follows this structure:

```markdown
## Description
Brief overview of what needs to be implemented

## Requirements
**Location:** file paths
**Files:** list of files to create
Detailed technical requirements

## Test Plan
List of test cases to implement

## Acceptance Criteria
- [ ] Checkboxes for completion criteria
```

### Labels Strategy

Current label categories:
- **Layer**: `application`, `domain`, `infrastructure`
- **Bounded Context**: `common`, `session`, `user`, etc.
- **Type**: `interface`, `service`, `handler`, `repository`
- **Purpose**: `validation`, `logging`, `routing`, etc.

## Best Practices

### Before Creating Issues

1. **Review the preview** - Always run `npm run preview` first
2. **Check repository access** - Ensure you have write permissions
3. **Plan your labels** - Consider your project's labeling strategy
4. **Backup existing issues** - If you have important issues, export them first

### During Creation

1. **Monitor rate limits** - The script includes delays, but watch for errors
2. **Check for duplicates** - Make sure you're not creating duplicate issues
3. **Verify repository** - Double-check you're targeting the right repo

### After Creation

1. **Review created issues** - Check that formatting looks correct
2. **Assign to team members** - Distribute work appropriately
3. **Set up project boards** - Organize issues into sprints/milestones
4. **Link related issues** - Create dependencies where needed

## Troubleshooting

### Common Issues

**"Configuration validation failed"**
- Check that REPO_OWNER and REPO_NAME are set correctly
- Verify your GitHub token is in the .env file

**"Bad credentials" errors**
- Regenerate your GitHub token
- Ensure the token has the right scopes

**"Not found" errors**
- Verify repository name and owner
- Check that you have access to the repository

**Rate limiting**
- The script includes 1-second delays by default
- Increase delays if you hit rate limits
- GitHub allows 5,000 requests/hour for authenticated users

### Getting Help

1. Check the error message carefully
2. Verify your GitHub token permissions
3. Test with a small number of issues first
4. Check GitHub's API status if you're having widespread issues

## File Structure

```
.
├── bulk-create-github-issues.js    # Main creation script
├── github-issues-data.js           # Issue definitions
├── preview-issues.js               # Preview script
├── package.json                    # Dependencies and scripts
├── .env.example                    # Environment template
├── .env                           # Your environment (create this)
├── README.md                      # Detailed documentation
└── USAGE_GUIDE.md                 # This file
```

## Next Steps

After creating your issues:

1. **Organize into milestones** - Group related issues
2. **Create project boards** - Visualize your workflow
3. **Assign team members** - Distribute the work
4. **Set up CI/CD** - Prepare for implementation
5. **Define done criteria** - Clarify what "complete" means for each issue

Happy issue creating! 🚀
