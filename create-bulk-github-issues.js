[Application] Common | CommandBus Interface

Description

Introduce a common CommandBus interface that defines the contract for executing application-layer commands. This interface is used by all bounded contexts (e.g., session) and provides a consistent execution surface.

Requirements

Location:

application/common/ports/

Files:

CommandBus.ts

CommandBus.ts

Export interface:

export interface CommandBus {
  execute(command: DomainCommand): Promise<CommandResult>;
}


DomainCommand comes from the domain layer.

CommandResult reuses application/common/dto/Results.ts.

Test Plan

Create application/common/ports/__tests__/command-bus.spec.ts:

type_only_contract: Compile-time test (dummy implementation satisfies interface).

result_type_enforced: Returning wrong type fails compilation.

Acceptance Criteria

File exists at specified path.

Interface exported via @/application/common/ports.

Tests pass in CI.

Shall I generate the EventBus ticket next?

You said:
yes
ChatGPT said:

Here’s the EventBus ticket in ready-to-copy markdown:

[Application] Common | EventBus Interface + InMemory Implementation

Description

Provide a reusable EventBus interface for publishing domain events asynchronously, and an in-memory implementation for testing and development. The bus supports multiple handlers and isolates failures.

Requirements

Location:

application/common/ports/

application/common/events/

Files:

EventBus.ts

InMemoryEventBus.ts

EventBus.ts

Export interface:

export interface EventBus {
  publish(events: readonly DomainEvent[]): Promise<void>;
}


InMemoryEventBus.ts

Implementation with:

addHandler(handler: (event: DomainEvent) => Promise<void>): void

publish(events) → invokes each handler sequentially, catches errors, logs them.

Async, non-blocking of command path.

Test Plan

Create application/common/events/__tests__/in-memory-event-bus.spec.ts:

publishes_to_all_handlers: Both handlers called for the same event.

isolates_handler_failure: One handler throws → others still invoked.

supports_multiple_events: Batch delivered in order.

Acceptance Criteria

Files exist at specified paths.

Interface exported via @/application/common/ports.

InMemory implementation available for unit tests.

Tests pass in CI.

Do you want me to continue with the TaskBroker ticket next?

You said:
yes
ChatGPT said:

[Application] Common | TaskBroker Interface

Description

Introduce a TaskBroker application-layer port used to enqueue commands for serialized, resilient execution when sessions are live. The broker is a thin abstraction over infrastructure (e.g., BullMQ), providing:

Per-session ordering via groupingKey (typically sessionId)

Delayed scheduling for timers/autopilot

Priority lanes for urgent ops (e.g., lock, return-all)

Idempotency hints via dedupeKey (typically commandId)

This port is infrastructure-agnostic and will be implemented in the infra layer. Application code must not import infra libraries directly.

Requirements

Location:

application/common/ports/

Files:

TaskBroker.ts

index.ts (re-export from application/common/ports)

TaskBroker.ts

Export interface:

export interface TaskBroker {
  enqueue(
    command: DomainCommand,
    opts?: {
      groupingKey?: string;   // required for per-session FIFO; usually the sessionId
      delayMs?: number;       // schedule for future execution
      priority?: number;      // lower number = higher priority (infra-dependent)
      dedupeKey?: string;     // idempotency key; usually command.commandId
      metadata?: {
        correlationId?: string; // for observability only; broker need not use it
        source?: 'API' | 'SCHEDULER' | 'WEBHOOK'; // provenance tag for logs/metrics
      };
    }
  ): Promise<void>;
}


Notes:

groupingKey should be provided by callers for state-mutating session commands to ensure single-writer semantics per session.

delayMs will be used by the infra implementation to schedule the job (e.g., delayed job).

priority is advisory; actual semantics depend on the infra adapter.

dedupeKey allows the adapter to drop duplicates before execution when supported.

The interface must not leak infra-specific types.

index.ts

Re-export TaskBroker for easy imports.

Usage Guidance (Documentation Comments)

When session is RUNNING/PAUSED or the command triggers external effects, call:

await taskBroker.enqueue(command, {
  groupingKey: command.payload.sessionId,
  dedupeKey: command.commandId,
  priority: 5,
  metadata: { correlationId: command.correlationId, source: 'API' }
});


For timed/autopilot flows, the process manager should call:

await taskBroker.enqueue(command, {
  groupingKey: sessionId,
  delayMs: msUntilDue,
  dedupeKey: command.commandId,
  metadata: { correlationId, source: 'SCHEDULER' }
});

Test Plan

Create application/common/ports/__tests__/task-broker.spec.ts:

type_contract_exports_interface
Import TaskBroker and ensure a dummy class implementing it type-checks.

options_are_optional
Call enqueue(command) with no opts and with each combination of opts fields; compile-time checks should pass.

does_not_leak_infra_types
Verify file has no imports from infra packages (e.g., bullmq, redis, kafkajs) via a simple lint rule or comment in test.

doc_examples_compile
Inline the two usage snippets above in a .ts test file to ensure they type-check against the interface.

Acceptance Criteria

TaskBroker.ts and index.ts exist at specified paths.

Interface matches the specified signature and does not import any infra-specific libraries.

Re-export available via @/application/common/ports.

Unit tests compile and pass in CI (type-contract tests included).

Short JSDoc comments included to guide callers on groupingKey, delayMs, priority, dedupeKey, and metadata usage.

You said:
continue
ChatGPT said:
Thought for a second
Thought for 7s

[Application] Common | Scheduler Interface

Description

Introduce a Scheduler application-layer port for time-based orchestration. The scheduler allows process managers (autopilot) and APIs to schedule a domain command to execute at a specific future time. The infrastructure (e.g., BullMQ delayed jobs) will implement this port. Scheduled commands must re-enter the same per-session serialized pipeline (via TaskBroker) to preserve ordering with ad-hoc commands.

Requirements

Location:

application/common/ports/

Files:

Scheduler.ts

index.ts (re-export from application/common/ports)

Scheduler.ts

Export interface:

export interface Scheduler {
  /**
   * Schedule a domain command to run at a specific time.
   * Implementations MUST ensure the command will be enqueued back
   * through the application's TaskBroker using the same groupingKey,
   * so ordering with ad-hoc commands is preserved.
   */
  schedule(
    command: DomainCommand,
    runAt: Date,
    groupingKey: string, // typically the sessionId
    opts?: {
      dedupeKey?: string; // usually command.commandId
      priority?: number;  // advisory; infra-specific
      metadata?: { correlationId?: string; source?: 'SCHEDULER' | 'API' | 'WEBHOOK' }
    }
  ): Promise<void>;
}


Notes:

groupingKey is required to maintain per-session FIFO ordering.

Implementations should store enough info to survive restarts (infra concern).

dedupeKey prevents duplicate schedules for the same logical operation.

The interface must not import infra packages.

index.ts

Re-export Scheduler.

Usage Guidance (Documentation Comments)

Autopilot example (inside a process manager reacting to RoundStarted):

await scheduler.schedule(
  endCurrentRoundCmd,
  new Date(roundStartedAtMs + durationMs),
  endCurrentRoundCmd.payload.sessionId,
  { dedupeKey: endCurrentRoundCmd.commandId, metadata: { correlationId, source: 'SCHEDULER' } }
);


Short-delay timers (< 5s) may still use the same interface; infra can decide whether to use delayed jobs or a near-term timer wheel.

Test Plan

Create application/common/ports/__tests__/scheduler.spec.ts:

type_contract_exports_interface
Import Scheduler and ensure a dummy class implementing it type-checks.

requires_groupingKey
Compile-time: calling schedule(cmd, runAt) without groupingKey fails type-check.

accepts_optional_opts
Calls with/without opts compile; all optional fields are optional.

does_not_leak_infra_types
Lint or static check that Scheduler.ts has no imports from infra libraries.

doc_example_compiles
Copy the usage snippet into the test file to ensure it type-checks.

Acceptance Criteria

Scheduler.ts and index.ts exist at specified paths.

Interface matches the specified signature and contains guidance comments.

No infra-specific imports (e.g., bullmq, redis) in the file.

Re-export available via @/application/common/ports.

Unit tests compile and pass in CI.

You said:
next
ChatGPT said:
Thought for a second
Thought for 4 seconds

[Application] Common | Repository Base Contract

Description

Provide a minimal, reusable Repository base contract that bounded contexts can extend (e.g., SessionRepository). Repositories operate on primitive DTOs (serialized forms of aggregates) to keep the application layer decoupled from persistence and the domain object graph.

Requirements

Location:

application/common/ports/

Files:

Repository.ts

index.ts (re-export from application/common/ports)

Repository.ts

Export generic base types for repositories operating on primitives:

// Primitive DTO contract that repositories persist/retrieve.
export interface Repository<TPrimitives, TId extends string = string> {
  findById(id: TId): Promise<TPrimitives | null>;
  save(dto: TPrimitives): Promise<void>;
}

// Optional: typed error contracts to standardize infra error mapping.
export class RepositoryError extends Error {
  constructor(message: string, public readonly ctx?: Record<string, unknown>) {
    super(message);
    this.name = 'RepositoryError';
  }
}

export class ConcurrencyError extends RepositoryError {
  constructor(message = 'Optimistic concurrency conflict', ctx?: Record<string, unknown>) {
    super(message, ctx);
    this.name = 'ConcurrencyError';
  }
}

export class NotFoundError extends RepositoryError {
  constructor(message = 'Entity not found', ctx?: Record<string, unknown>) {
    super(message, ctx);
    this.name = 'NotFoundError';
  }
}


Notes:

Keep the contract small and stable: findById, save.

Work exclusively with primitive DTOs; no domain objects here.

Error types are optional but recommended for consistent error mapping in the app layer.

index.ts

Re-export Repository, RepositoryError, ConcurrencyError, NotFoundError.

Test Plan

Create application/common/ports/__tests__/repository-base.spec.ts:

type_contract_minimal
A dummy repository class implementing Repository<{ id: string; v: number }> compiles and can be instantiated.

findById_returns_null_when_missing
In a dummy in-memory implementation, call findById('missing') → expect null.

save_and_find_roundtrip
Save a DTO and then findById returns an equal but not same-reference object (defensive copy if applicable).

custom_id_type
Implement Repository<MyDto, MyId> where type MyId = string & { __brand: 'SessionId' } compiles (verifies generic ID flexibility).

error_types_exported
Import and instantiate RepositoryError, ConcurrencyError, NotFoundError; ensure .name is correct.

Acceptance Criteria

Files exist at specified paths.

Repository generic interface exactly matches the specified signature.

No domain objects or infra-specific imports appear in Repository.ts.

Re-exports available via @/application/common/ports.

Unit tests compile and pass in CI.

You said:
next
ChatGPT said:
Thought for a second
Thought for a few seconds

[Application] Common | CommandValidator Service

Description

Introduce a reusable CommandValidator service that performs fail-fast structural validation on incoming DomainCommands before they reach handlers. The goal is to eliminate malformed inputs at the application boundary and keep domain code focused on business rules. Validation includes presence/shape checks, UUID format, timestamp sanity, and session-target checks for session-scoped commands.

Requirements

Location:

application/common/services/

Files:

CommandValidator.ts

__tests__/CommandValidator.spec.ts

(re-export from) application/common/index.ts

CommandValidator.ts

Export interface and default implementation:

export interface CommandValidator {
  ensureValid(command: DomainCommand): void;  // throws on invalid
}

export class DefaultCommandValidator implements CommandValidator {
  ensureValid(command: DomainCommand): void;
}


Validation rules (throw Error with clear message including code prefix; codes listed below):

Base presence

command is object (VAL.COMMAND_NOT_OBJECT)

command.kind non-empty string (VAL.KIND_REQUIRED)

command.commandId present + UUID v4 (VAL.COMMAND_ID_INVALID)

command.issuedAt is finite number, ≥ 0, and <= Date.now() + 60_000 to allow slight skew (VAL.ISSUED_AT_INVALID)

Actor

command.actor present (VAL.ACTOR_REQUIRED)

command.actor.role in known roles (SYSTEM|HOST|CO_HOST|ADMIN|ATTENDEE) (VAL.ACTOR_ROLE_INVALID)

Session-scoped commands

If command payload includes a sessionId (most do), require non-empty string UUID v4 (VAL.SESSION_ID_INVALID)

Expected version (optional)

If expectedVersion present, must be integer ≥ 0 (VAL.EXPECTED_VERSION_INVALID)

Correlation/Causation (optional)

If provided, correlationId and causationId must be UUID v4 (VAL.CORRELATION_ID_INVALID, VAL.CAUSATION_ID_INVALID)

Version field

version (schema version) must be integer ≥ 1 if present (VAL.SCHEMA_VERSION_INVALID)

Error codes (prefix all thrown messages with these codes so logs/alerts can aggregate):

VAL.COMMAND_NOT_OBJECT, VAL.KIND_REQUIRED, VAL.COMMAND_ID_INVALID,
VAL.ISSUED_AT_INVALID, VAL.ACTOR_REQUIRED, VAL.ACTOR_ROLE_INVALID,
VAL.SESSION_ID_INVALID, VAL.EXPECTED_VERSION_INVALID,
VAL.CORRELATION_ID_INVALID, VAL.CAUSATION_ID_INVALID,
VAL.SCHEMA_VERSION_INVALID.

Notes:

Do not import infra packages.

Use server time (Date.now()) for skew checks; do not trust client clocks.

Keep this validator pure (no I/O).

application/common/index.ts

Re-export DefaultCommandValidator and CommandValidator.

Test Plan

Create application/common/services/__tests__/CommandValidator.spec.ts with unit tests:

valid_minimal_command_passes
Builds a minimal valid command and ensureValid does not throw.

rejects_non_object
ensureValid(undefined as any) throws with code VAL.COMMAND_NOT_OBJECT.

rejects_missing_kind
Missing/empty kind throws VAL.KIND_REQUIRED.

rejects_bad_commandId
Non-UUID v4 commandId throws VAL.COMMAND_ID_INVALID.

rejects_bad_issuedAt
NaN/negative or too-far-future timestamp throws VAL.ISSUED_AT_INVALID.

rejects_missing_actor
Missing actor throws VAL.ACTOR_REQUIRED.

rejects_invalid_actor_role
Unknown role throws VAL.ACTOR_ROLE_INVALID.

sessionId_must_be_uuid_when_present
Payload with non-UUID sessionId throws VAL.SESSION_ID_INVALID.

expectedVersion_must_be_non_negative_int
Floats/negative/NaN throw VAL.EXPECTED_VERSION_INVALID.

correlation_and_causation_ids_if_present_must_be_uuid
Bad IDs throw respective codes.

schema_version_if_present_must_be_int_ge_1
Bad values throw VAL.SCHEMA_VERSION_INVALID.

does_not_mutate_input
Deep-freeze input; validator must not modify it.

Acceptance Criteria

DefaultCommandValidator enforces all rules above and throws Error with the specified code prefixes in the message.

Export available via @/application/common.

No infrastructure or domain persistence imports.

All tests pass in CI (including failure case assertions on error messages).

You said:
next
ChatGPT said:
Thought for a second
Thought for 6s

[Application] Common | IdempotencyLedger Service

Description

Introduce a reusable IdempotencyLedger service to guarantee that a DomainCommand identified by commandId is applied at most once by the application layer, even under retries or redeliveries (e.g., queue replays). The ledger is checked before executing domain logic and marked after a successful save + event publish. This prevents duplicate state changes and duplicate event emissions.

Requirements

Location:

application/common/services/

Files:

IdempotencyLedger.ts

__tests__/IdempotencyLedger.spec.ts

(re-export from) application/common/index.ts

IdempotencyLedger.ts

Export interface and default in-memory implementation:

export interface IdempotencyLedger {
  /**
   * Return true if the commandId has already been applied (and should be skipped).
   * This MUST be a fast, side-effect-free check.
   */
  isApplied(commandId: string): Promise<boolean>;

  /**
   * Mark commandId as applied. Must be called only after a successful
   * aggregate save and event publish to avoid false positives.
   */
  markApplied(commandId: string, opts?: { ttlMs?: number }): Promise<void>;
}

/**
 * MVP in-memory implementation suitable for unit tests and dev runs.
 * Production implementations should use a durable store (e.g., Redis) in infra.
 */
export class InMemoryIdempotencyLedger implements IdempotencyLedger {
  // constructor can accept clock injection for tests if desired
  constructor(opts?: { now?: () => number }) {}

  isApplied(commandId: string): Promise<boolean>;
  markApplied(commandId: string, opts?: { ttlMs?: number }): Promise<void>;
}


Behavior notes:

commandId must be treated as a case-sensitive key (UUID v4 strings).

markApplied supports optional ttlMs for automatic expiry (defaults to 24h for MVP; value may be tuned later).

In-memory impl may use a Map<string, number> storing expiresAtMs for each commandId, and GC expired entries on reads/writes.

No external I/O in the default implementation; infra will supply a durable adapter later.

Integration Guidance (JSDoc in file):

Handler usage pattern (pseudocode):

if (await ledger.isApplied(command.commandId)) {
  // Optionally log "IdempotencyReplayIgnored"
  return { success: true, events: [], aggregateVersion: undefined };
}

// ... execute domain op, save, publish events ...

await ledger.markApplied(command.commandId);


Mark only after successfully saving the aggregate and publishing events to prevent marking failed attempts as applied.

application/common/index.ts

Re-export IdempotencyLedger and InMemoryIdempotencyLedger.

Test Plan

Create application/common/services/__tests__/IdempotencyLedger.spec.ts:

returns_false_for_unknown_id
isApplied('c-1') → false.

marks_and_reads_true
markApplied('c-1') then isApplied('c-1') → true.

respects_ttl_expiry
Use injected clock or fake timers: markApplied('c-1', { ttlMs: 50 }); advance time > 50ms; isApplied('c-1') → false.

idempotent_marking
Calling markApplied('c-1') twice does not throw; isApplied('c-1') stays true.

case_sensitive_keys
Mark 'C-1'; isApplied('c-1') remains false.

gc_on_read_write
Add multiple entries with short TTLs; after expiry, reads/writes clean up internal map (indirectly asserted by size checks or by memory inspection helper in test-only subclass).

Acceptance Criteria

IdempotencyLedger.ts and tests exist at the specified paths.

InMemoryIdempotencyLedger implements the exact interface and honors ttlMs.

No infrastructure dependencies (no redis, bullmq, etc.).

Re-exports available via @/application/common.

All unit tests pass in CI.

You said:
next
ChatGPT said:
Thought for a second
Thought for 4 seconds

[Application] Common | OutboxService (Tx-Safe Event/Task Outbox)

Description

Introduce an OutboxService to guarantee reliable delivery of side effects (event publishing and task enqueueing) after aggregate state is saved. The outbox pattern prevents the “saved state but lost message” problem by persisting outbox records in the same transaction as the aggregate write, and flushing them asynchronously. For MVP, define the application-layer contract and a simple in-memory implementation for tests/dev; infrastructure implementations (DB-backed) will come later.

Requirements

Location:

application/common/services/

Files:

OutboxService.ts

__tests__/OutboxService.spec.ts

(re-export from) application/common/index.ts

OutboxService.ts

Export types:

OutboxRecord:

id: string (UUID v4)

type: 'EVENT' | 'TASK'

correlationId: string

causationId?: string

payload: Readonly<DomainEvent | DomainCommand>

createdAt: number (server time ms)

status: 'PENDING' | 'DELIVERED' | 'FAILED'

attempts: number

lastError?: string

AppendOptions:

batchId?: string (to append multiple records under the same transactional batch)

Export interface:

export interface OutboxService {
  /**
   * Append records to the outbox within the caller’s transaction boundary.
   * The implementation MUST be safe to call multiple times in the same tx.
   */
  append(records: ReadonlyArray<OutboxRecord>, opts?: AppendOptions): Promise<void>;

  /**
   * Flush a bounded number of PENDING outbox records, delivering each to the appropriate sink:
   * - EVENT → EventBus.publish([...])
   * - TASK  → TaskBroker.enqueue(...)
   * The implementation should mark records DELIVERED on success and FAILED with lastError on error.
   */
  flushOnce(limit?: number): Promise<{ delivered: number; failed: number }>;
}


Export in-memory MVP implementation (dev/test only):

InMemoryOutboxService:

Stores records in a local array/map.

Requires EventBus and TaskBroker instances in its constructor.

append(...) pushes records into memory.

flushOnce(limit = 100):

Takes up to limit PENDING records (stable order by createdAt then id).

For EVENT records: call eventBus.publish([payload as DomainEvent]).

For TASK records: call taskBroker.enqueue(payload as DomainCommand, { groupingKey: (payload as any)?.payload?.sessionId, // best effort; safe if missing dedupeKey: (payload as any)?.commandId, metadata: { correlationId: (payload as any)?.correlationId, source: 'API' } }).

On success: mark DELIVERED, increment attempts.

On error: mark FAILED, increment attempts, capture lastError.

Notes:

No external persistence in MVP impl (pure in-memory).

The application layer only defines the contract; durable (DB) impls are infra concerns.

append() is designed to be called from the same transaction as aggregate save(...) (real tx semantics apply only in infra impls).

application/common/index.ts

Re-export OutboxService, OutboxRecord, AppendOptions, InMemoryOutboxService.

Integration Guidance (Documentation Comments)

In a command handler:

Save aggregate state.

Build outbox records:

One EVENT record per domain event to publish (or batch as a single record with an array if your EventBus supports it).

One TASK record for each command that must be enqueued (e.g., provider operations, scheduled follow-ups if you don’t call Scheduler directly).

await outbox.append(records, { batchId: command.correlationId }).

A background job calls outbox.flushOnce() repeatedly.

Important: For the in-memory MVP, outbox isn’t transactional with repository writes—use only in tests/dev. Production needs a DB-backed impl (infra).

Test Plan

Create application/common/services/__tests__/OutboxService.spec.ts:

append_and_flush_events_success
Given one EVENT record and a spy EventBus, flushOnce() publishes event and marks record DELIVERED.

append_and_flush_tasks_success
Given one TASK record and a spy TaskBroker, flushOnce() enqueues command and marks record DELIVERED.

flush_respects_limit_and_order
With 3 records (older first), flushOnce(2) delivers the first two in order; the third remains PENDING.

failed_publish_marks_failed_and_keeps_error
EventBus throws → record marked FAILED with lastError, attempts===1.

subsequent_flush_can_retry_failed
On second flushOnce(), if handler succeeds, record transitions to DELIVERED.

idempotent_append_call
Calling append() twice with the same record id does not create duplicates (assert by reference or count).

does_not_import_infra
Static check (or linter rule) that the file does not import redis, bullmq, etc.

Acceptance Criteria

Outbox types and interface exist and are exported via @/application/common.

InMemoryOutboxService compiles, passes tests, and demonstrates the delivery flow to EventBus and TaskBroker.

flushOnce(limit) behavior matches the spec (ordering, status transitions, error capture).

No infrastructure dependencies are imported from the common module.

All unit tests pass in CI.

You said:
next
ChatGPT said:
Thought for a couple of seconds
Thought for a few seconds

[Application] Common | ContextLogger (Structured Logging)

Description

Introduce a ContextLogger utility that emits structured logs for application-layer operations. It standardizes keys (e.g., correlationId, commandId, sessionId, eventId, kind, actor.role) and provides helper methods to log command start/success/failure and event publication. This ensures consistent observability across all bounded contexts and allows easy querying in log backends.

Requirements

Location:

application/common/instrumentation/

Files:

ContextLogger.ts

__tests__/ContextLogger.spec.ts

(re-export from) application/common/index.ts

ContextLogger.ts

Export the public interface and a default implementation:

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogSink {
  log(level: LogLevel, message: string, fields?: Record<string, unknown>): void;
}

export interface ContextLogger {
  with(fields: Record<string, unknown>): ContextLogger;

  // Generic log
  log(level: LogLevel, message: string, fields?: Record<string, unknown>): void;

  // Command lifecycle helpers
  logCommandStart(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; actorRole?: string;
  }): void;

  logCommandSuccess(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; aggregateVersion?: number; durationMs?: number;
  }): void;

  logCommandFailure(fields: {
    commandId: string; kind: string; sessionId?: string;
    correlationId?: string; error: unknown; durationMs?: number;
  }): void;

  // Event publishing helper
  logEventsPublished(fields: {
    correlationId?: string; sessionId?: string;
    eventCount: number; kinds?: readonly string[];
  }): void;
}

export class DefaultContextLogger implements ContextLogger { /* see notes */ }


Behavior & conventions:

Immutable context: with(fields) returns a new logger instance that merges the extra fields as defaults for subsequent logs (does not mutate original).

Standard keys included in every log when available:

correlationId, commandId, eventId (when relevant),

sessionId, roundId, roomId,

kind (command or event kind),

actor.role.

DefaultContextLogger delegates to a provided LogSink (constructor param). The sink is the only place that touches console, Pino, Winston, etc. (keeps common module infra-agnostic).

Messages are short action verbs; context fields carry the details.

No external imports from infra packages.

Guidance (JSDoc examples)

In a handler:

const start = Date.now();
logger.logCommandStart({ commandId, kind, sessionId, correlationId, actorRole });

try {
  const result = await commandBus.execute(cmd);
  logger.logCommandSuccess({
    commandId, kind, sessionId, correlationId,
    aggregateVersion: result.aggregateVersion,
    durationMs: Date.now() - start,
  });
} catch (error) {
  logger.logCommandFailure({
    commandId, kind, sessionId, correlationId,
    error, durationMs: Date.now() - start,
  });
  throw error;
}


After publishing:

logger.logEventsPublished({
  correlationId, sessionId,
  eventCount: events.length,
  kinds: events.map(e => e.kind),
});


application/common/index.ts

Re-export ContextLogger, DefaultContextLogger, LogSink, LogLevel.

Test Plan

Create application/common/instrumentation/__tests__/ContextLogger.spec.ts:

with_returns_new_logger_and_merges_fields
logger.with({ a: 1 }) returns a new instance; original unchanged; subsequent calls include a:1.

log_includes_standard_context_keys
Using logCommandStart with fields results in sink receiving a log with keys: correlationId, commandId, kind, sessionId, actor.role (when provided).

success_and_failure_emit_expected_fields
logCommandSuccess includes aggregateVersion and durationMs; logCommandFailure includes error and durationMs.

events_published_includes_counts_and_kinds
Verify sink was called with eventCount and kinds[].

no_infra_imports
Static check/lint that file has no imports from logging libraries.

immutability
Calling with() does not affect logs emitted by previous logger instance.

Acceptance Criteria

ContextLogger interface and DefaultContextLogger exist and compile.

No direct dependency on specific logging frameworks in the common module.

JSDoc usage examples present in the file.

Tests pass in CI and confirm field presence, immutability, and sink delegation.

You said:
next
ChatGPT said:

[Application | SessionCommandHandler Routing Setup]

Description

Implement the SessionCommandHandler as the central orchestrator for all session-related commands. It will perform validation, route commands to the appropriate category handler (lifecycle, rounds, participants, rooms, host), manage aggregate loading/saving, and publish resulting events via the EventBus.

Requirements

Location:

application/session/handlers/SessionCommandHandler.ts

Dependencies:

ports/SessionRepository (to load/save sessions)

ports/EventBus (to publish domain events)

services/CommandValidator (structural + invariant checks)

services/IdempotencyLedger (prevent replays)

Responsibilities:

handle(command: DomainCommand): Promise<Result>

Validate structure with CommandValidator.

Check deduplication with IdempotencyLedger.

Extract sessionId (if applicable).

Route by command.kind to specialized category handlers.

Save updated aggregate via SessionRepository.

Publish collected domain events via EventBus.

Return Results.Success or Results.Failure.

Routing Categories:

lifecycle.ts – Start/Pause/Resume/Complete/Cancel

participants.ts – Add/Join/Leave/Seat/Restore

rooms.ts – CreateBreakout/Assign/Release/Rotate

rounds.ts – AddRound/StartRound/EndCurrentRound

host.ts – SetHost

Error Handling:

Convert thrown domain errors into Results.Failure.

Attach correlation/causation IDs to logs and responses.

Test Plan

Create application/session/__tests__/session-command-handler.spec.ts:

happy_path_lifecycle
Given StartSessionCommand, repository returns scheduled session → expect SessionStartedEvent published, result.success true.

invalid_command_structure
Given malformed command object → expect Results.Failure with validation error.

idempotent_duplicate
Given duplicate commandId → IdempotencyLedger prevents reprocessing → expect Results.Success with no new events.

aggregate_not_found
Given command referencing missing session → expect failure with SessionNotFoundError.

event_bus_failure_isolated
Simulate EventBus.publishEvents throwing → command still returns success, error logged.

Acceptance Criteria

SessionCommandHandler routes all supported commands via category handlers.

Validates structure and deduplication before execution.

Saves updated aggregate state with correct version.

Publishes all resulting domain events asynchronously.

Returns consistent Results objects.

Test suite covers success, failure, idempotency, and integration paths.

Would you like me to continue with the next ticket for handlers/routing/lifecycle.ts?

You said:
next
ChatGPT said:

[Application] Session | Routing: lifecycle.ts

Description

Implement the lifecycle routing module that handles session lifecycle commands by delegating to the Session aggregate. This module is invoked by SessionCommandHandler and encapsulates the orchestration for lifecycle transitions. It must load the aggregate (via repository), call the appropriate domain method, collect uncommitted events, persist the new state, and return the result.

Supported commands for MVP:

CreateSession

StartSession

PauseSession

ResumeSession

CompleteSession

CancelSession

Requirements

Location:

application/session/handlers/routing/lifecycle.ts

Files:

lifecycle.ts

__tests__/lifecycle.spec.ts

lifecycle.ts

Export a narrow API used by SessionCommandHandler:

handleCreateSession(command, deps): Promise<ResultSuccess | ResultFailure>

handleLifecycle(command, deps): Promise<ResultSuccess | ResultFailure>

Types:

deps contains:

{
  repo: SessionRepository;
  validator: CommandValidator;
  // optional hooks for observability
  log?: ContextLogger;
  now?: () => number; // test seam for time-based fields
}


handleCreateSession:

Validate command structure with validator.ensureValid(...).

Convert payload primitives to domain-friendly primitives only if needed by domain factory.

Invoke domain factory Session.create(...) (domain already implemented).

Extract uncommitted events from the new aggregate.

Persist via repo.save(session.toPrimitives()).

Return { success: true, events, aggregateVersion }.

handleLifecycle:

Validate command structure.

Extract sessionId (command.payload.sessionId).

Load existing session via repo.findById(sessionId); return failure if null.

Rehydrate with Session.fromPrimitives(dto).

Switch on command.kind:

StartSession → session.startSession(Instant.fromPrimitives(command.issuedAt))

PauseSession → session.pauseSession(Instant...)

ResumeSession → session.resumeSession(Instant...)

CompleteSession → session.completeSession(Instant...)

CancelSession → session.cancelSession(Instant...)

Collect events → repo.save(updated.toPrimitives()).

Return { success: true, events, aggregateVersion: updated.getVersion() }.

Error handling:

If repo returns null: map to Results.Failure with code: 'APP.SESSION_NOT_FOUND' and ctx: { sessionId }.

Domain violations (invalid transition) should be caught and mapped to Results.Failure with code: 'APP.DOMAIN_VIOLATION' and include .message.

Unknown command.kind → Results.Failure with code: 'APP.UNSUPPORTED_COMMAND'.

Observability (optional but recommended):

If log provided, emit log.logCommandStart/Success/Failure with commandId, kind, sessionId, correlationId, and durationMs.

Test Plan

Create application/session/handlers/routing/__tests__/lifecycle.spec.ts:

create_session_happy_path

Arrange: valid CreateSession command with required payload (sessionId, config, createdByUserId, mainRoomId).

Stub repo.save to resolve.

Assert: result.success, events contain SessionCreated (or equivalent), aggregateVersion === 1.

start_session_happy_path

Arrange: repo returns SCHEDULED session DTO.

Act: handleLifecycle(StartSession, deps).

Assert: success, events include SessionStarted, repo.save called with updated state, version incremented.

pause_resume_flow

Arrange: RUNNING → PauseSession then ResumeSession.

Assert: each call returns success and publishes SessionPaused/SessionResumed.

complete_session_happy_path

Arrange: RUNNING → CompleteSession.

Assert: success, SessionCompleted event present.

cancel_from_scheduled

Arrange: SCHEDULED → CancelSession.

Assert: success, SessionCanceled event present.

session_not_found

Arrange: repo.findById returns null.

Assert: failure code: 'APP.SESSION_NOT_FOUND'.

invalid_transition_fails

Arrange: already RUNNING; invoke StartSession.

Domain throws; assert failure code: 'APP.DOMAIN_VIOLATION' and error message propagated.

unsupported_command_kind

Arrange: call handleLifecycle with a non-lifecycle kind.

Assert: failure code: 'APP.UNSUPPORTED_COMMAND'.

observability_hooks_called

With a spy ContextLogger, assert logCommandStart/Success called with expected fields and that durationMs is positive.

Acceptance Criteria

lifecycle.ts exposes handleCreateSession and handleLifecycle with the specified signatures.

All specified lifecycle commands route correctly to the Session aggregate and return a Result with events and version.

Proper failure modes for not-found, invalid transition, and unsupported commands are implemented with error codes.

Repository usage matches the pattern: findById (when needed) → domain call → save.

Optional observability hooks integrate without being mandatory.

Unit tests cover all happy paths and failure cases; tests pass in CI.

You said:
next
ChatGPT said:
Thought for a second
Thought for 10s

[Application] Session | Routing: participants.ts

Description

Implement the participants routing module that handles participant-related commands by delegating to the Session aggregate. This module is invoked by SessionCommandHandler and encapsulates orchestration for participant lifecycle within a session, including joining, leaving, seating, and restoring reservations.

Supported MVP commands:

AddParticipant

JoinParticipant

SoftLeaveParticipant

HardLeaveParticipant

SeatParticipant

RestoreParticipant

Requirements

Location:

application/session/handlers/routing/participants.ts

Files:

participants.ts

__tests__/participants.spec.ts

participants.ts

Export an API used by SessionCommandHandler:

handleParticipants(command, deps): Promise<ResultSuccess | ResultFailure>

Types:

deps contains:

{
  repo: SessionRepository;
  validator: CommandValidator;
  log?: ContextLogger;       // optional for observability
  now?: () => number;        // test seam for time-based fields
}


General flow (inside handleParticipants):

validator.ensureValid(command) (fail-fast).

Extract sessionId from command.payload.sessionId; load DTO via repo.findById(sessionId). If null, return failure { code: 'APP.SESSION_NOT_FOUND', ctx: { sessionId } }.

Rehydrate domain: const session = Session.fromPrimitives(dto).

Switch on command.kind and delegate to the aggregate:

AddParticipant →
session.addParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt), payload.role)

JoinParticipant →
session.joinParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt))

SoftLeaveParticipant →
session.softLeaveParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt))

HardLeaveParticipant →
session.hardLeaveParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt))

SeatParticipant →
session.seatParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt), /* optional: target room/seat from payload if domain supports */)

RestoreParticipant →
session.restoreParticipant(ParticipantId.fromPrimitives(payload.participantId), Instant.fromPrimitives(command.issuedAt))

Collect uncommitted events; await repo.save(updated.toPrimitives()).

Return { success: true, events, aggregateVersion: updated.getVersion() }.

Error handling mapping:

Missing session → APP.SESSION_NOT_FOUND.

Domain rule violations (e.g., duplicate active join, seat capacity exceeded, invalid restore window) → APP.DOMAIN_VIOLATION with message and minimal ctx (e.g., { participantId, sessionId }).

Unsupported kind routed here → APP.UNSUPPORTED_COMMAND.

Observability (optional):

If log exists, wrap with timing:

log.logCommandStart({ commandId, kind, sessionId, correlationId, actorRole })

On success/failure, call corresponding logger and include durationMs.

Notes:

This router remains domain-agnostic: no direct VideoSDK calls, no side-effects beyond repository + event publication triggered by the handler.

Do not perform deduplication here; idempotency is handled by SessionCommandHandler before routing.

Test Plan

Create application/session/handlers/routing/__tests__/participants.spec.ts:

add_participant_happy_path
Given a valid AddParticipant and an existing session DTO, expect success, repo.save called once, and an event like ParticipantAdded present.

join_idempotent_behavior_surface
If domain treats repeated joins as no-ops when already present, assert events reflect either a single ParticipantJoined or no new events depending on domain behavior (router just forwards).

soft_leave_then_restore
Issue SoftLeaveParticipant then RestoreParticipant; expect both succeed and corresponding events emitted in order.

hard_leave_removes_presence
After HardLeaveParticipant, subsequent RestoreParticipant should fail with APP.DOMAIN_VIOLATION (assuming domain forbids restore after hard leave).

seat_participant_validates_via_domain
Attempt to seat beyond capacity → domain throws → router returns ResultFailure with code: 'APP.DOMAIN_VIOLATION'.

session_not_found
Repo returns null → APP.SESSION_NOT_FOUND.

unsupported_kind
Pass a non-participant command to this module → returns APP.UNSUPPORTED_COMMAND.

observability_hooks_called
With a spy ContextLogger, assert logCommandStart/Success (or Failure) are called and durationMs is positive.

Acceptance Criteria

participants.ts exports handleParticipants with the specified signature and behavior.

All listed participant commands route correctly to the Session aggregate and return a Result with events and updated version.

Error mappings for not-found, domain violation, and unsupported commands are implemented.

Repository calls follow: findById → domain call → save.

Optional observability hooks are supported without being required.

Unit tests cover all happy paths and failure cases; pass in CI.

You said:
next
ChatGPT said:
Thought for a second
Thought for 6s

[Application] Session | Routing: rooms.ts

Description

Implement the rooms routing module that handles breakout room–related commands by delegating to the Session aggregate. This module is called by SessionCommandHandler and encapsulates orchestration for room lifecycle and seating operations. It must load the aggregate (via repository), call the appropriate domain method, persist changes, and return emitted events.

Supported MVP commands:

CreateBreakoutRoom

AssignParticipantToRoom

ReleaseSeatByParticipant

RotateBreakouts

ReleaseAllReservedSeats

(No direct VideoSDK calls here; the app layer remains domain- and persistence-focused. External side-effects are triggered elsewhere by subscribers to the domain events.)

Requirements

Location:

application/session/handlers/routing/rooms.ts

Files:

rooms.ts

__tests__/rooms.spec.ts

rooms.ts

Export API used by SessionCommandHandler:

handleRooms(command, deps): Promise<ResultSuccess | ResultFailure>

Types:

deps contains:

{
  repo: SessionRepository;
  validator: CommandValidator;
  log?: ContextLogger;   // optional for observability
  now?: () => number;    // test seam for time-based fields
}


Flow (inside handleRooms):

validator.ensureValid(command) (fail-fast).

Extract sessionId from command.payload.sessionId.

Load DTO via repo.findById(sessionId). If null, return failure with code: 'APP.SESSION_NOT_FOUND', ctx: { sessionId }.

Rehydrate: const session = Session.fromPrimitives(dto).

Switch on command.kind and delegate to aggregate methods (names illustrative—use the domain’s actual API):

CreateBreakoutRoom →
session.createBreakoutRoom(RoomConfig.fromPrimitives(payload.config), Instant.fromMillis(command.issuedAt))

AssignParticipantToRoom →
session.assignParticipantToRoom(ParticipantId.from(payload.participantId), RoomId.from(payload.roomId), Instant.fromMillis(command.issuedAt))

ReleaseSeatByParticipant →
session.releaseSeatByParticipant(ParticipantId.from(payload.participantId), Instant.fromMillis(command.issuedAt))

RotateBreakouts →
session.rotateBreakouts(Instant.fromMillis(command.issuedAt))

ReleaseAllReservedSeats →
session.releaseAllReservedSeats(Instant.fromMillis(command.issuedAt))

Collect events; await repo.save(updated.toPrimitives()).

Return { success: true, events, aggregateVersion: updated.getVersion() }.

Error handling mapping:

Missing session → APP.SESSION_NOT_FOUND.

Domain violations (capacity exceeded, room not found, participant not seated, invalid rotation state) → APP.DOMAIN_VIOLATION with .message and minimal ctx (e.g., { sessionId, roomId?, participantId? }).

Unsupported kind routed here → APP.UNSUPPORTED_COMMAND.

Observability (optional):

If log provided, wrap handler with timing:

log.logCommandStart({ commandId, kind, sessionId, correlationId, actorRole })

On success/failure, call log.logCommandSuccess/Failure with durationMs.

Notes:

Router performs no cross-room batching; any micro-batching is performed by the worker/queue level.

Do not implement idempotency here (done at handler boundary).

Test Plan

Create application/session/handlers/routing/__tests__/rooms.spec.ts:

create_breakout_room_happy_path
Repo returns session DTO in a state that permits room creation → result.success, event like BreakoutRoomCreated present, repo.save called.

assign_participant_to_room_happy_path
Valid participant and target room; success and event ParticipantAssignedToRoom emitted.

release_seat_by_participant_happy_path
Participant currently seated; after release, event SeatReleased (or domain-equivalent) emitted, success true.

rotate_breakouts_happy_path
When domain permits rotation, event BreakoutsRotated emitted; save called once.

release_all_reserved_seats_happy_path
Reserved seats exist; event(s) emitted; version increments.

session_not_found
repo.findById returns null → failure with code: 'APP.SESSION_NOT_FOUND'.

room_capacity_violation
Assign beyond capacity → domain throws → router returns failure with code: 'APP.DOMAIN_VIOLATION' and includes ctx.

room_not_found_or_participant_unknown
Assign to non-existing room / participant not in session → failure APP.DOMAIN_VIOLATION.

unsupported_kind
Passing a non-room command → failure APP.UNSUPPORTED_COMMAND.

observability_hooks
With spy logger, assert logCommandStart and logCommandSuccess/Failure called and durationMs > 0.

Acceptance Criteria

rooms.ts exports handleRooms with the specified signature and behavior.

All listed room commands route correctly to the Session aggregate and return Result with events and updated version.

Proper failure mappings for not-found, domain violations, and unsupported commands implemented.

Repository usage strictly follows: findById → domain invoke → save.

Optional observability hooks function when provided.

Unit tests cover all happy paths and failure cases; CI green.

You said:
next
ChatGPT said:
Thought for a second
Thought for 6s

[Application] Session | Routing: rounds.ts

Description

Implement the rounds routing module that handles round-related commands by delegating to the Session aggregate. This router is invoked by SessionCommandHandler and encapsulates orchestration for agenda/round control: adding a round, starting one, and ending the current round. It loads the aggregate (via repository), calls the appropriate domain method, persists the new state, and returns emitted events.

Supported MVP commands:

AddRound

StartRound

EndCurrentRound

(No external side-effects here; fan-out and timers are handled by other subscribers/processes.)

Requirements

Location:

application/session/handlers/routing/rounds.ts

Files:

rounds.ts

__tests__/rounds.spec.ts

rounds.ts

Export API used by SessionCommandHandler:

handleRounds(command, deps): Promise<ResultSuccess | ResultFailure>

Types:

deps contains:

{
  repo: SessionRepository;
  validator: CommandValidator;
  log?: ContextLogger;   // optional for observability
  now?: () => number;    // test seam for time-based fields
}


Flow (inside handleRounds):

validator.ensureValid(command) (fail-fast).

Extract sessionId from command.payload.sessionId.

Load DTO via repo.findById(sessionId). If null, return failure { code: 'APP.SESSION_NOT_FOUND', ctx: { sessionId } }.

Rehydrate domain: const session = Session.fromPrimitives(dto).

Switch on command.kind and delegate to aggregate methods (names illustrative—use domain’s actual API):

AddRound →
session.addRound(RoundConfig.fromPrimitives(payload.round), Instant.fromMillis(command.issuedAt))

StartRound →
session.startRound(RoundId.from(payload.roundId), Instant.fromMillis(command.issuedAt))

EndCurrentRound →
session.endCurrentRound(Instant.fromMillis(command.issuedAt))

Collect uncommitted events.

await repo.save(updated.toPrimitives()).

Return { success: true, events, aggregateVersion: updated.getVersion() }.

Error handling mapping:

Missing session → APP.SESSION_NOT_FOUND.

Domain violations (e.g., add during invalid state, starting a non-existent round, no active round to end) → APP.DOMAIN_VIOLATION with .message and minimal ctx (e.g., { sessionId, roundId? }).

Unsupported kind routed here → APP.UNSUPPORTED_COMMAND.

Observability (optional):

If log provided, wrap with timing:

log.logCommandStart({ commandId, kind, sessionId, correlationId, actorRole })

On success/failure, call log.logCommandSuccess/Failure with durationMs.

Notes:

This router is domain- and persistence-focused; no timers created here. Scheduling of auto-end/return-all is handled by a process manager subscribing to events (separate ticket).

Test Plan

Create application/session/handlers/routing/__tests__/rounds.spec.ts:

add_round_happy_path
Repo returns a valid session DTO; call AddRound; expect success, events include RoundAdded (or equivalent), repo.save invoked once.

start_round_happy_path
Session has the specified round; call StartRound; expect success, event RoundStarted, version incremented.

end_current_round_happy_path
An active round exists; call EndCurrentRound; expect success and event RoundEnded.

session_not_found
repo.findById returns null; expect failure with code: 'APP.SESSION_NOT_FOUND'.

start_round_missing_round_fails
Domain throws (round not found) → router returns failure with code: 'APP.DOMAIN_VIOLATION' and ctx.roundId.

end_without_active_round_fails
Domain throws (no active round) → failure APP.DOMAIN_VIOLATION.

unsupported_kind
Pass a non-round command → failure APP.UNSUPPORTED_COMMAND.

observability_hooks
With a spy logger, assert logCommandStart and logCommandSuccess/Failure are called and durationMs > 0.

Acceptance Criteria

rounds.ts exports handleRounds with the specified signature and behavior.

All listed round commands route correctly to the Session aggregate and return Result with events and updated version.

Proper failure mappings for not-found, domain violations, and unsupported commands implemented.

Repository usage strictly follows: findById → domain invoke → save.

Optional observability hooks function when provided.

Unit tests cover all happy paths and failure cases; CI green.

You said:
next
ChatGPT said:
Thought for a second
Thought for 9s

[Application] Session | Port: SessionRepository

Description

Define the SessionRepository port for the session bounded context. The repository operates on primitive DTOs (serialized aggregate state) to keep the application layer decoupled from persistence. This contract is consumed by the SessionCommandHandler and routing modules (lifecycle, participants, rooms, rounds). Implementations (infrastructure) will provide DB, event-store, or in-memory adapters later.

Requirements

Location:

application/session/ports/

Files:

SessionRepository.ts

index.ts (re-export)

SessionRepository.ts

Export the DTO type import (from domain) and the repository interface:

import type { SessionPrimitives } from '@/domain/session'; (adjust import path to your domain package)

Interface:

export interface SessionRepository {
  /**
   * Load a session aggregate as primitives.
   * @returns null if the session does not exist.
   */
  findById(sessionId: string): Promise<SessionPrimitives | null>;

  /**
   * Persist the full primitive snapshot for the session.
   * Implementations SHOULD use optimistic concurrency if supported by the store.
   */
  save(session: SessionPrimitives): Promise<void>;
}


Notes:

Keep the surface minimal: findById, save only.

Work exclusively with SessionPrimitives; no domain objects.

Implementations should strive for optimistic concurrency (e.g., version check).

Do not import infrastructure libraries here.

index.ts

Re-export SessionRepository.

Usage Guidance (Documentation Comments)

Handlers call:

Load: const dto = await repo.findById(sessionId); → return failure if null.

Rehydrate: const session = Session.fromPrimitives(dto);

After domain mutation: await repo.save(session.toPrimitives());

If the underlying store supports versions, pass/compare dto.version on update for optimistic lock enforcement.

Test Plan

Create application/session/ports/__tests__/SessionRepository.contract.spec.ts (contract tests using a fake in-memory repo stub inside the test file):

findById_returns_null_when_missing
await repo.findById('missing') → null.

save_then_find_roundtrip
Save a valid SessionPrimitives → findById(sessionId) returns a deep-equal copy (not the same reference).

does_not_mutate_input
Freeze the DTO before save; ensure repository does not modify the object (store a copy internally).

version_field_roundtrip
When DTO includes version, roundtrip preserves the value.

type_only_contract
Ensure that SessionRepository can be implemented by a dummy class with the exact method signatures (compile-time check).

Note: This file defines the contract and validates expected semantics. Production implementations are delivered in the infrastructure layer and will have their own integration tests.

Acceptance Criteria

SessionRepository.ts and index.ts exist at the specified paths.

Interface exactly matches the method signatures above and uses SessionPrimitives.

No infrastructure imports (e.g., SQL, Redis, BullMQ) in the port.

Contract test file compiles and passes (using a test-local in-memory stub).

All application-layer callers (handlers/routing) can import SessionRepository from @/application/session/ports.

You said:
next
ChatGPT said:
Thought for a second
Thought for 12s

[Application] Session | Process Manager: Autopilot (timers & follow-up commands)

Description

Implement the Autopilot process manager for the session bounded context. It reacts to domain events and issues follow-up commands (often delayed) to orchestrate flows automatically (no manual host actions in MVP). The process manager must be stateless, subscribe to the EventBus, and use the Scheduler (and/or TaskBroker) to enqueue commands back into the same per-session serialized pipeline.

Initial scenarios to cover for MVP:

When a round starts, schedule EndCurrentRound at startedAt + durationMs.

(Optional if your domain emits it) When breakouts are distributed/created, schedule a ReturnAll at the expected end of the breakout phase.

Requirements

Location:

application/session/processes/

Files:

AutopilotProcess.ts

__tests__/AutopilotProcess.spec.ts

(re-export) application/session/index.ts

AutopilotProcess.ts

Export class:

export interface AutopilotDeps {
  scheduler: Scheduler;          // schedules delayed commands
  taskBroker: TaskBroker;        // immediate enqueue (no delay) if needed
  log?: ContextLogger;           // optional instrumentation
  now?: () => number;            // test seam for current time
}

export class AutopilotProcess {
  constructor(private readonly deps: AutopilotDeps) {}

  /**
   * Attach the process manager to the EventBus.
   * Returns an unsubscribe function if your EventBus supports it; else no-op.
   */
  subscribe(eventBus: EventBus): () => void;

  /**
   * Handle one domain event (exposed for unit testing).
   */
  handle(event: DomainEvent): Promise<void>;
}


Behavior:

subscribe(eventBus) registers a single handler that calls this.handle(event) for each event.

handle(event) pattern-matches on event.kind (or strongly typed narrows if available):

RoundStarted

Extract sessionId, roundId, durationMs, occurredAt.

Build EndCurrentRoundCommand with:

commandId: new UUID v4

correlationId: event.correlationId (propagate)

causationId: event.eventId

issuedAt: now()

payload: { sessionId }

Compute runAt = new Date(event.occurredAt + durationMs).

Call:

await scheduler.schedule(endCmd, runAt, sessionId, {
  dedupeKey: endCmd.commandId,
  priority: 5,
  metadata: { correlationId: endCmd.correlationId, source: 'SCHEDULER' }
});


Log via log?.log('info', 'autopilot.scheduled_end_current_round', { sessionId, roundId, runAtMs: runAt.getTime() }).

BreakoutsDistributed / RoomsCreatedForRound (optional)

If your domain emits an event signaling the breakout phase start, compute the planned end using config on the event payload; schedule ReturnAll at occurredAt + plannedMs similarly to the above.

Never mutate domain state directly. Only schedule/queue commands.

Notes:

Use Scheduler for delayed actions and TaskBroker for immediate follow-ups (rare in MVP).

Always set groupingKey = sessionId to preserve per-session ordering.

Propagate correlation/causation IDs from the triggering event to the emitted command.

application/session/index.ts

Re-export AutopilotProcess.

Test Plan

Create application/session/processes/__tests__/AutopilotProcess.spec.ts:

subscribes_and_handles_round_started

Arrange: in-memory EventBus, mocked Scheduler, spy ContextLogger.

Act: process.subscribe(bus); bus.publish([roundStartedEvent]);

Assert: scheduler.schedule called once with:

command.kind === 'EndCurrentRound'

groupingKey === sessionId

runAt === occurredAt + durationMs

opts.metadata.correlationId === event.correlationId

opts.dedupeKey === command.commandId

Logger called with autopilot.scheduled_end_current_round.

propagates_correlation_and_causation

Assert the scheduled command’s correlationId equals the event’s correlationId, and causationId equals eventId.

optional_breakout_flow_schedule (skip if event not present in your domain)

Given BreakoutsDistributed (or equivalent), assert a ReturnAll command is scheduled at planned end time.

ignores_unrelated_events

Publish a non-round/non-breakout event → scheduler.schedule not called.

robust_to_scheduler_failure

Make scheduler.schedule throw once → process catches and logs error (does not crash).

Acceptance Criteria

AutopilotProcess subscribes to the EventBus and handles RoundStarted by scheduling EndCurrentRound with correct timing, grouping, and correlation.

(If applicable) BreakoutsDistributed (or equivalent) leads to scheduling ReturnAll.

No domain state mutations are performed by the process manager.

Uses only Scheduler/TaskBroker ports; no infra imports.

Optional logging emits structured entries with sessionId, roundId, correlationId, and scheduled runAt.

All tests pass in CI.

You said:
next
ChatGPT said:
Thought for a few seconds
Thought for 11s

[Application] Common | Tracer (Span API for Commands & Publishing)

Description

Introduce a lightweight, framework-agnostic Tracer to create spans around critical application-layer operations (command execution, repository I/O, event publishing). The goal is consistent tracing across modules without binding to a specific vendor (OTel, Datadog, etc.). Infra can later provide an adapter that maps these spans to a concrete tracer.

Requirements

Location:

application/common/instrumentation/

Files:

Tracer.ts

__tests__/Tracer.spec.ts

(re-export from) application/common/index.ts

Tracer.ts

Export types and default no-op implementation:

export interface Span {
  setTag(key: string, value: unknown): void;
  recordException(error: unknown): void;
  end(): void;
}

export interface Tracer {
  /**
   * Run the function within a new span named `name`.
   * Always ends the span, even if `fn` throws.
   */
  withSpan<T>(
    name: string,
    attributes: Record<string, unknown>,
    fn: (span: Span) => Promise<T> | T
  ): Promise<T>;

  /**
   * Create a child span without executing a function immediately.
   * Caller is responsible for calling `end()`.
   */
  startSpan(name: string, attributes?: Record<string, unknown>): Span;
}

/**
 * No-op tracer for tests/dev; does nothing but preserve API shape.
 */
export class NoopTracer implements Tracer {
  withSpan<T>(name: string, attributes: Record<string, unknown>, fn: (span: Span) => Promise<T> | T): Promise<T>;
  startSpan(name: string, attributes?: Record<string, unknown>): Span;
}


Conventions (document via JSDoc in file):

Span names (recommended):

app.command.handle (top-level around SessionCommandHandler.execute)

app.repo.findById, app.repo.save

app.events.publish

app.process.autopilot.handle

Standard attributes (add when available):

correlation.id, command.id, event.id

session.id, round.id, room.id

command.kind / event.kind

actor.role

On error: span.recordException(error) and also set error=true.

Notes:

No imports from tracing vendors in the common module.

The NoopTracer returns Span objects whose methods are safe no-ops.

application/common/index.ts

Re-export Tracer, Span, NoopTracer.

Usage Guidance (Documentation Comments)

Wrap command execution:

await tracer.withSpan('app.command.handle', {
  'correlation.id': command.correlationId,
  'command.id': command.commandId,
  'command.kind': command.kind,
  'session.id': command.payload?.sessionId
}, async (span) => {
  // validate, idempotency check, repo.load, domain.apply, repo.save
});


Wrap event publishing:

await tracer.withSpan('app.events.publish', {
  'correlation.id': correlationId,
  'session.id': sessionId,
  count: events.length
}, async () => eventBus.publish(events));

Test Plan

Create application/common/instrumentation/__tests__/Tracer.spec.ts:

withSpan_runs_fn_and_ends_span
Spy span object; ensure fn result is returned and end() called exactly once.

withSpan_records_exception
Make fn throw; assert recordException called and end() still called; exception re-thrown to caller.

startSpan_manual_end
startSpan() returns a span; calling end() twice is a no-op (no throw).

standard_attribute_guidelines_documented
Assert exported constants or comments include attribute keys listed above (either via a small exported STANDARD_ATTR_KEYS or comment presence check).

noop_tracer_does_not_throw
Using NoopTracer with all APIs does not throw and returns the underlying function result.

Acceptance Criteria

Tracer and Span interfaces and NoopTracer exist at specified paths.

No external tracing vendor imports in the common module.

JSDoc includes recommended span names and standard attributes.

Tests pass in CI, verifying span lifecycle and error handling behavior.

You said:
next
ChatGPT said:
Thought for a second
Thought for 7s

[Application] Common | Metrics (Counters, Histogram, Gauges)

Description

Introduce a small, framework-agnostic Metrics API for the application layer to record operational telemetry. The API exposes counters, histograms, and gauges used by handlers, routers, and process managers. Provide a no-op default implementation for dev/tests. Infrastructure can later plug in Prometheus, OpenTelemetry, or any vendor.

Requirements

Location:

application/common/instrumentation/

Files:

Metrics.ts

__tests__/Metrics.spec.ts

(re-export from) application/common/index.ts

Metrics.ts

Export metric handle types and a registry interface:

export interface Counter {
  inc(labels?: Record<string, string>, value?: number): void;
}
export interface Histogram {
  observe(labels: Record<string, string> | undefined, valueMs: number): void;
}
export interface Gauge {
  set(labels: Record<string, string> | undefined, value: number): void;
}

export interface Metrics {
  // factories return cached instances by name
  counter(name: string, help?: string, labelNames?: readonly string[]): Counter;
  histogram(name: string, help?: string, labelNames?: readonly string[]): Histogram;
  gauge(name: string, help?: string, labelNames?: readonly string[]): Gauge;
}

export class NoopMetrics implements Metrics { /* returns no-op handles */ }


Standard metric names & labels (document via JSDoc and export as constants):

Counters

app_commands_total
Labels: command_kind, actor_role, source (API|SCHEDULER|WEBHOOK), result (success|failure|idempotent)

app_events_published_total
Labels: event_kind

Histogram

app_command_latency_ms
Labels: command_kind, actor_role
Observed in milliseconds

Gauges

app_session_queue_lag_ms
Labels: session_id
Set by worker when dequeuing

NoopMetrics must:

Cache and return the same handle per (name), so repeated calls don’t allocate endlessly.

Return handles whose methods are safe no-ops.

application/common/index.ts

Re-export Metrics, Counter, Histogram, Gauge, NoopMetrics.

Re-export constants for standard metric names (e.g., METRIC_APP_COMMANDS_TOTAL, …) and recommended label keys.

Usage Guidance (Documentation Comments)

In SessionCommandHandler.execute:

const endTimer = Date.now();
try {
  // ... handle command ...
  metrics.counter('app_commands_total').inc(
    { command_kind: command.kind, actor_role: command.actor.role, source: 'API', result: 'success' }
  );
  metrics.histogram('app_command_latency_ms').observe(
    { command_kind: command.kind, actor_role: command.actor.role },
    Date.now() - endTimer
  );
} catch (e) {
  metrics.counter('app_commands_total').inc(
    { command_kind: command.kind, actor_role: command.actor.role, source: 'API', result: 'failure' }
  );
  throw e;
}


In a queue worker:

metrics.gauge('app_session_queue_lag_ms').set({ session_id: sessionId }, lagMs);

Test Plan

Create application/common/instrumentation/__tests__/Metrics.spec.ts:

factory_returns_cached_handles
metrics.counter('app_commands_total') returns the same instance across calls.

noop_handles_do_not_throw
Calling .inc(), .observe(), .set() on handles from NoopMetrics does not throw.

jsdoc_constants_exported
Import METRIC_APP_COMMANDS_TOTAL, METRIC_APP_COMMAND_LATENCY_MS, etc.; verify they are defined strings.

labels_are_optional_where_specified
inc(undefined), observe(undefined, 1), set(undefined, 0) do not throw.

performance_no_excess_alloc (lightweight check)
Create many handles for the same name in a loop; verify referential equality to ensure caching.

Acceptance Criteria

Metrics API and NoopMetrics exist and compile; no vendor-specific imports.

Constants for standard metric names/labels are exported and documented.

NoopMetrics caches handles by name and is safe to use in tests/dev.

Example usage is documented in JSDoc.

Test suite passes in CI.

You said:
Next
ChatGPT said:
Thought for a second
Thought for 5s

[Application] Common | Results DTO (Success/Failure contract)

Description

Define a small, consistent Result contract for application-layer operations (command handlers and routers). This prevents ad-hoc return shapes, simplifies controller/worker code, and standardizes error propagation. Handlers will return either a Success with emitted events and the latest aggregate version, or a Failure with a normalized error.

Requirements

Location:

application/common/dto/

Files:

Results.ts

__tests__/Results.spec.ts

(re-export from) application/common/index.ts

Results.ts

Export types and helper creators:

import type { DomainEvent } from '@/domain'; // adjust path to your domain export

export type ResultSuccess = {
  readonly success: true;
  readonly events: readonly DomainEvent[];
  readonly aggregateVersion: number | undefined; // undefined for no-op/idempotent cases
};

export type NormalizedError = {
  readonly code: string;                     // e.g., 'APP.SESSION_NOT_FOUND', 'APP.DOMAIN_VIOLATION'
  readonly message: string;                  // human-readable summary (safe to surface)
  readonly ctx?: Record<string, unknown>;    // minimal context (IDs only, no PII)
  readonly cause?: unknown;                  // optional raw cause (kept internal; not logged verbatim)
};

export type ResultFailure = {
  readonly success: false;
  readonly error: NormalizedError;
};

export type CommandResult = ResultSuccess | ResultFailure;

// Helpers
export const ok = (
  events: readonly DomainEvent[],
  aggregateVersion: number | undefined
): ResultSuccess => ({ success: true, events, aggregateVersion });

export const fail = (
  code: string,
  message: string,
  ctx?: Record<string, unknown>,
  cause?: unknown
): ResultFailure => ({ success: false, error: { code, message, ctx, cause } });


Notes & conventions:

aggregateVersion is the version after the operation. Use undefined when the handler intentionally did nothing (e.g., idempotent replay).

Keep ctx minimal (IDs, counts); do not place PII or large blobs.

code space is owned by the application layer (suggested prefixes):

APP.SESSION_NOT_FOUND, APP.DOMAIN_VIOLATION, APP.UNSUPPORTED_COMMAND, APP.VALIDATION_FAILED, APP.REPOSITORY_ERROR, APP.CONCURRENCY_CONFLICT.

Controllers/consumers may map code to HTTP status (e.g., 404 for not found, 409 for concurrency).

application/common/index.ts

Re-export CommandResult, ResultSuccess, ResultFailure, NormalizedError, ok, fail.

Usage Guidance (Documentation Comments)

In a handler (success):

return ok(events, updated.getVersion());


In a handler (fail fast):

return fail('APP.SESSION_NOT_FOUND', 'Session not found', { sessionId });


Idempotent replay (no new events):

return ok([], /* aggregateVersion */ undefined);

Test Plan

Create application/common/dto/__tests__/Results.spec.ts:

ok_returns_success_shape
Call ok([eventA], 3); assert success===true, events.length===1, aggregateVersion===3.

fail_returns_failure_shape
Call fail('APP.DOMAIN_VIOLATION','Invalid transition',{ sessionId:'s1' }); assert success===false, error.code/message/ctx present.

ok_allows_undefined_version
ok([], undefined) compiles and is valid (idempotent/no-op case).

narrowing_via_type_guard
In test, write a function that narrows result.success ? result.events : result.error; ensure TypeScript narrows correctly.

no_mutation_of_inputs
Freeze an events array; ensure ok() does not mutate it.

Acceptance Criteria

Results.ts exists and exports the specified types and helpers.

Re-exports available via @/application/common.

Handlers in the session module can import and use ok/fail without additional wiring.

Unit tests compile and pass in CI, validating shapes, narrowing, and immutability assumptions.

You said:
Next
ChatGPT said:
Thought for a second
Thought for 4 seconds

[Application] Common | Error Mapping (Mapping.ts)

Description

Introduce a centralized error mapping utility to convert thrown errors (validation, domain, repository/infra) into the normalized ResultFailure shape used by the application layer. This avoids ad-hoc error handling in handlers/routers and ensures consistent error codes/messages for controllers and workers.

Requirements

Location:

application/common/errors/

Files:

Mapping.ts

__tests__/Mapping.spec.ts

(re-export from) application/common/index.ts

Mapping.ts

Export types and functions:

import type { NormalizedError, ResultFailure } from '@/application/common/dto/Results';

// Optional domain error shapes (narrow as needed for your domain package)
export interface DomainErrorLike {
  readonly name?: string;
  readonly code?: string;                 // e.g., 'DOMAIN.INVALID_TRANSITION'
  readonly message: string;
  readonly ctx?: Record<string, unknown>;
}

export interface RepositoryErrorLike {
  readonly name?: string;                 // 'RepositoryError' | 'ConcurrencyError' | 'NotFoundError'
  readonly message: string;
  readonly ctx?: Record<string, unknown>;
}

export type ErrorKind =
  | 'VALIDATION'
  | 'DOMAIN'
  | 'NOT_FOUND'
  | 'CONCURRENCY'
  | 'REPOSITORY'
  | 'UNKNOWN';

export type MappingOptions = {
  defaultCodePrefix?: 'APP';              // allows future contexts to override
  includeCause?: boolean;                 // attach raw error object (kept internal)
  redact?: (ctx: Record<string, unknown>) => Record<string, unknown>; // PII scrubber
  correlationId?: string;                 // for logging hooks (not in NormalizedError)
};

export const mapErrorToNormalized = (err: unknown, opts?: MappingOptions): NormalizedError => {
  // implementation outline documented below
};

export const asFailure = (err: unknown, opts?: MappingOptions): ResultFailure => ({
  success: false,
  error: mapErrorToNormalized(err, opts),
});


Mapping rules (documented in comments and implemented in mapErrorToNormalized):

Validation errors (from DefaultCommandValidator):

If err.message starts with VAL. or err.code starts with VAL. →
code = 'APP.VALIDATION_FAILED', message = err.message, ctx = err.ctx || {}.

Domain errors:

If err.code starts with DOMAIN. or err.name === 'DomainError' →
code = 'APP.DOMAIN_VIOLATION', message = err.message, ctx = err.ctx || {}.

Repository errors:

If err.name === 'NotFoundError' → code = 'APP.SESSION_NOT_FOUND' (or generic 'APP.NOT_FOUND'), message = 'Entity not found', ctx = err.ctx || {}.

If err.name === 'ConcurrencyError' → code = 'APP.CONCURRENCY_CONFLICT', message = 'Optimistic concurrency conflict', ctx = err.ctx || {}.

Otherwise err.name === 'RepositoryError' → code = 'APP.REPOSITORY_ERROR', message = err.message.

Unknown/other:

code = 'APP.UNKNOWN', message = 'Unexpected error', and include a minimal ctx if safe (e.g., { name, message }).

Redaction:

If opts?.redact provided and ctx exists, run it to scrub PII/secrets before returning.

Cause inclusion:

Only include cause in the returned NormalizedError when opts?.includeCause === true.

Notes:

Keep this file framework and infra agnostic.

Do not log inside the mapper—logging is the responsibility of handlers using ContextLogger.

Make the code prefix (APP) configurable via MappingOptions.defaultCodePrefix for future bounded contexts.

application/common/index.ts

Re-export asFailure, mapErrorToNormalized, ErrorKind, MappingOptions.

Usage Guidance (Documentation Comments)

In SessionCommandHandler.execute:

try {
  // ... validate → repo.find → domain.apply → repo.save → publish ...
  return ok(events, updated.getVersion());
} catch (err) {
  logger?.logCommandFailure({ commandId, kind: command.kind, sessionId, correlationId, error: err, durationMs });
  return asFailure(err, { defaultCodePrefix: 'APP', includeCause: false });
}


In routers (e.g., lifecycle.ts), catch domain or repository errors and return asFailure(err, ...).

Test Plan

Create application/common/errors/__tests__/Mapping.spec.ts:

maps_validation_error
Given new Error('VAL.COMMAND_ID_INVALID: ...') → code === 'APP.VALIDATION_FAILED', message contains 'VAL.COMMAND_ID_INVALID'.

maps_domain_error_with_code
Given { name: 'DomainError', code: 'DOMAIN.INVALID_TRANSITION', message: '...' } → code === 'APP.DOMAIN_VIOLATION'.

maps_repository_not_found
Given { name: 'NotFoundError', message: '...' } → code === 'APP.SESSION_NOT_FOUND' (or 'APP.NOT_FOUND' if you choose generic).

maps_repository_concurrency
Given { name: 'ConcurrencyError', message: '...' } → code === 'APP.CONCURRENCY_CONFLICT'.

maps_generic_repository_error
{ name: 'RepositoryError', message: 'boom' } → code === 'APP.REPOSITORY_ERROR', message === 'boom'.

unknown_error_fallback
Given { name: 'TypeError', message: 'x' } → code === 'APP.UNKNOWN', message === 'Unexpected error'.

redaction_applied
Provide ctx with { email: '<EMAIL>' } and a redact function that removes email. Expect returned ctx to be redacted.

include_cause_false_by_default
asFailure(err) → result.error has no cause. With { includeCause: true } → cause present.

Acceptance Criteria

Mapping.ts exists and exports the specified API.

Mapping rules produce the expected codes/messages for validation, domain, repository, and unknown errors.

File contains no infra or framework imports.

Re-exports available via @/application/common.

Unit tests pass in CI.

You said:
Next
ChatGPT said:
Thought for a second
Thought for 7s

[Application] Session | Contract Tests (InMemory Repo + InMemory EventBus)

Description

Create end-to-end contract tests for the session bounded context using only application-layer ports and in-memory fakes. These tests validate that SessionCommandHandler + routing modules (lifecycle, participants, rooms, rounds) correctly load/save state, emit domain events, and return standardized Results. No infrastructure code (DB/queue) is used—only in-memory stubs for SessionRepository and EventBus.

Requirements

Location:

application/session/__tests__/contracts/

Files:

in-memory-session-repo.fake.ts

contract-happy-path.spec.ts

contract-errors-and-idempotency.spec.ts

in-memory-session-repo.fake.ts

Provide a minimal in-memory SessionRepository implementation for tests:

Internal store: Map<string, SessionPrimitives>

findById(id) → deep copy (defensive clone)

save(dto) → deep copy into map

Optional: support a version field if present; no real concurrency checks (that’s infra)

Export a helper makeRepoWith(dto?: SessionPrimitives) to seed state.

contract-happy-path.spec.ts

Boot the handler with:

repo = InMemorySessionRepository

eventBus = InMemoryEventBus

validator = DefaultCommandValidator

idempotency = InMemoryIdempotencyLedger

simple fakes for logger, tracer, metrics (no-ops)

Scenarios:

schedule_to_running_to_complete

CreateSession → StartSession → CompleteSession

Assert: ordered events (SessionCreated, SessionStarted, SessionCompleted), aggregateVersion increments, repo snapshot reflects final state.

round_lifecycle

AddRound → StartRound → EndCurrentRound

Assert: RoundAdded, RoundStarted, RoundEnded emitted; version increments exactly once per command.

basic_participant_flow

AddParticipant → JoinParticipant → SoftLeaveParticipant → RestoreParticipant

Assert: events present in order; repo state reflects presence after restore.

rooms_basics

CreateBreakoutRoom → AssignParticipantToRoom → ReleaseSeatByParticipant

Assert: events emitted; participant no longer seated after release.

event_bus_fanout_shape

Subscribe two handlers to InMemoryEventBus; assert both receive each emitted event (fan-out, failure isolation).

contract-errors-and-idempotency.spec.ts

Scenarios:

session_not_found

StartSession with unknown sessionId → ResultFailure with code: 'APP.SESSION_NOT_FOUND'.

domain_violation_examples

Attempt StartSession when already RUNNING → failure APP.DOMAIN_VIOLATION.

Attempt to assign participant to non-existent room → failure APP.DOMAIN_VIOLATION.

unsupported_command_routed_here

Send a kind not handled by any router → APP.UNSUPPORTED_COMMAND.

idempotent_duplicate_command

Same commandId re-submitted (e.g., JoinParticipant) → second call returns success: true with events: [] and aggregateVersion: undefined.

Assert IdempotencyLedger was checked before execution.

event_bus_handler_failure_isolated

One event handler throws; publishing still completes and other handlers are called (no retries here—unit verifies isolation).

Acceptance Criteria

In-memory repo fake provides defensive copies on findById/save and has no side-effects outside its map.

Contract tests cover all listed happy-path and failure scenarios.

SessionCommandHandler + routers interact with SessionRepository and InMemoryEventBus only (no infra).

All assertions pass in CI:

Correct event order, result shapes, and version increments.

Proper error codes (APP.SESSION_NOT_FOUND, APP.DOMAIN_VIOLATION, APP.UNSUPPORTED_COMMAND).

Idempotent replay yields no new events and aggregateVersion: undefined.

No imports from infrastructure libraries in this test suite.

You said:
Next
ChatGPT said:
Thought for a second
Thought for 8s

[Application] Session | Routing: host.ts

Description

Implement the host routing module that handles host/ownership commands by delegating to the Session aggregate. This router is invoked by SessionCommandHandler and encapsulates orchestration for setting or clearing the session host (and optional co-host if your domain supports it). It must load the aggregate via the repository, invoke the appropriate domain method, persist, and return emitted events.

Supported MVP commands:

SetHost

UnsetHost (include only if your domain supports explicit clearing; otherwise omit)

Requirements

Location:

application/session/handlers/routing/host.ts

Files:

host.ts

__tests__/host.spec.ts

host.ts

Export API used by SessionCommandHandler:

handleHost(command, deps): Promise<ResultSuccess | ResultFailure>

Types:

deps contains:

{
  repo: SessionRepository;
  validator: CommandValidator;
  log?: ContextLogger;   // optional observability
  now?: () => number;    // test seam
}


Flow:

validator.ensureValid(command) (fail-fast).

Extract sessionId from command.payload.sessionId.

const dto = await repo.findById(sessionId);

If null, return fail('APP.SESSION_NOT_FOUND', 'Session not found', { sessionId }).

Rehydrate: const session = Session.fromPrimitives(dto).

switch (command.kind):

SetHost →
session.setHost(ParticipantId.from(command.payload.participantId), Instant.fromMillis(command.issuedAt))

UnsetHost (if supported) →
session.unsetHost(Instant.fromMillis(command.issuedAt))

Default → return fail('APP.UNSUPPORTED_COMMAND', 'Unsupported host command', { kind: command.kind }).

Collect uncommitted events → await repo.save(updated.toPrimitives()).

Return ok(events, updated.getVersion()).

Error mapping:

Domain violations (e.g., setting host to a participant not in session, or invalid state transitions) → fail('APP.DOMAIN_VIOLATION', err.message, { sessionId, participantId?: string }, err).

Observability (optional):

If log provided, wrap with timing and call log.logCommandStart/Success/Failure with commandId, kind, sessionId, correlationId, actorRole, durationMs.

Test Plan

Create application/session/handlers/routing/__tests__/host.spec.ts:

set_host_happy_path

Arrange: existing session DTO with the participant present; build SetHost command.

Act: handleHost.

Assert: success, event like HostSet (or domain-equivalent) emitted, repo.save called once, version increments.

set_host_participant_not_in_session_fails

Arrange: participantId not part of session.

Act: handleHost.

Assert: failure with code: 'APP.DOMAIN_VIOLATION' and ctx includes participantId.

unset_host_happy_path (if supported)

Arrange: session has a host.

Act: UnsetHost.

Assert: success, event like HostUnset emitted, version increments.

session_not_found

Arrange: repo returns null.

Act: handleHost.

Assert: failure with code: 'APP.SESSION_NOT_FOUND' and ctx.sessionId.

unsupported_kind

Arrange: call with unrelated kind.

Assert: failure with code: 'APP.UNSUPPORTED_COMMAND'.

observability_hooks

With a spy logger, assert logCommandStart/logCommandSuccess (or Failure) are called; durationMs > 0.

Acceptance Criteria

host.ts exports handleHost with the specified signature and behavior.

Supported host commands route to the Session aggregate and return ok(...) with events and updated version.

Proper failure mappings for not-found, domain violations, and unsupported commands.

Repository usage strictly follows: findById → domain invoke → save.

Optional observability hooks function when provided.

Unit tests cover all happy paths and failure cases; CI green.